<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>K8S 集群资源报告 - {{ .ReportDate }}</title>
    <style type="text/css">
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 
                'Open Sans', 'Helvetica Neue', "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            background-color: #f5f5f5;
            font-size: 14px;
            line-height: 1.5;
        }
        .container {
            max-width: 960px;
            margin: 20px auto;
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 24px rgba(0,0,0,0.06);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
            color: white;
            padding: 24px 30px;
            border-bottom: 1px solid #e2e8f0;
        }
        .header-title {
            font-size: 1.7em;
            font-weight: 600;
            margin: 0;
        }
        .header-date {
            font-size: 0.95em;
            opacity: 0.9;
            margin-top: 5px;
        }
        .content {
            padding: 30px;
        }
        .cluster-section {
            margin-bottom: 35px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
        }
        .cluster-header {
            background-color: #f1f5f9;
            padding: 15px 20px;
            font-size: 1.3em;
            font-weight: 600;
            color: #1e293b;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .cluster-header-info {
            font-size: 0.8em;
            font-weight: normal;
            color: #64748b;
        }
        .cluster-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            padding: 20px;
            background-color: #ffffff;
        }
        .summary-item {
            background-color: #f8fafc;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            border: 1px solid #e2e8f0;
        }
        .summary-label {
            font-size: 0.9em;
            color: #64748b;
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        .summary-value {
            font-size: 1.4em;
            font-weight: 600;
            color: #1e293b;
        }
        .usage-bar-container {
            width: 100%;
            background-color: #f0f0f0;
            height: 10px;
            border-radius: 5px;
            margin: 5px 0;
            overflow: hidden;
        }
        .usage-bar {
            height: 100%;
            border-radius: 5px;
            background-color: #4caf50;
            transition: width 0.3s;
        }
        .usage-bar.underutilized-usage {
            background-color: #93c5fd; /* 低分配率 - 浅蓝色 */
        }
        .usage-bar.high-usage {
            background-color: #facc15; /* 警告 - 黄色 */
        }
        .usage-bar.danger-usage {
            background-color: #f87171; /* 危险 - 红色 */
        }
        .usage-bar.emergency-usage {
            background-color: #ef4444; /* 紧急 - 深红色 */
        }
        
        /* Add normal-usage class if not already defined */
        .usage-bar.normal-usage {
            background-color: #4ade80; /* 正常 - 绿色 */
        }

        /* 添加简略波动图样式 */
        .trend-container {
            margin-top: 8px;
            width: 100%;
            border-collapse: collapse;
            border-radius: 4px;
            overflow: hidden;
        }
        .trend-container td {
            height: 15px;
            padding: 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .trend-day {
            width: 14.28%; /* 7天平分 */
        }
        .trend-label {
            font-size: 10px;
            color: #718096;
            text-align: right;
            margin-top: 2px;
            display: block;
        }
        
        /* 趋势波动图新样式 - 使用和主图表相同的颜色 */
        .trend-day.bar-underutilized {
            background-color: #93c5fd; /* 低分配率 - 浅蓝色 */
        }
        .trend-day.bar-normal {
            background-color: #4ade80; /* 正常 - 绿色 */
        }
        .trend-day.bar-warning {
            background-color: #facc15; /* 警告 - 黄色 */
        }
        .trend-day.bar-critical {
            background-color: #f87171; /* 危险 - 红色 */
        }
        .trend-day.bar-emergency {
            background-color: #ef4444; /* 紧急 - 深红色 */
        }
        
        /* 保留旧的样式类以兼容 */
        .cpu-trend-underutilized {
            background-color: rgba(147, 197, 253, 0.5); /* 低分配率 - 浅蓝色 */
        }
        .cpu-trend-normal {
            background-color: rgba(74, 222, 128, 0.4); /* 正常 - 绿色 */
        }
        .cpu-trend-high {
            background-color: rgba(250, 204, 21, 0.6); /* 警告 - 黄色 */
        }
        .cpu-trend-critical {
            background-color: rgba(248, 113, 113, 0.7); /* 危险 - 红色 */
        }
        .cpu-trend-emergency {
            background-color: rgba(239, 68, 68, 0.8); /* 紧急 - 深红色 */
        }
        /* 内存分配率颜色 */
        .memory-trend-underutilized {
            background-color: rgba(147, 197, 253, 0.5); /* 低分配率 - 浅蓝色 */
        }
        .memory-trend-normal {
            background-color: rgba(74, 222, 128, 0.4); /* 正常 - 绿色 */
        }
        .memory-trend-high {
            background-color: rgba(250, 204, 21, 0.6); /* 警告 - 黄色 */
        }
        .memory-trend-critical {
            background-color: rgba(248, 113, 113, 0.7); /* 危险 - 红色 */
        }
        .memory-trend-emergency {
            background-color: rgba(239, 68, 68, 0.8); /* 紧急 - 深红色 */
        }
        .high-usage {
            background-color: #facc15; /* 黄色 - 警告 */
        }
        .danger-usage {
            background-color: #f87171; /* 红色 - 危险 */
        }
        .emergency-usage {
            background-color: #ef4444; /* 深红色 - 紧急 */
        }
        .summary-item.underutilized {
            background-color: #dbeafe;
            border: 1px solid #93c5fd;
        }
        .summary-item.warning {
            background-color: #fef9c3;
            border: 1px solid #facc15;
        }
        .summary-item.critical {
            background-color: #fee2e2;
            border: 1px solid #f87171;
        }
        .summary-item.emergency {
            background-color: #fecaca;
            border: 1px solid #ef4444;
        }
        .summary-value.underutilized {
            color: #1e40af; /* 蓝色 */
        }
        .summary-value.warning {
            color: #854d0e; /* 深黄色 */
        }
        .summary-value.critical {
            color: #991b1b; /* 深红色 */
        }
        .summary-value.emergency {
            color: #7f1d1d; /* 暗红色 */
        }

        /* 健康概览样式 - 从安全报告模板中复制 */
        .health-summary {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            border-bottom: 1px solid #e2e8f0;
            margin-bottom: 20px;
        }
        .health-card {
            padding: 20px 24px;
            text-align: center;
            border-right: 1px solid #e2e8f0;
        }
        .health-card:last-child {
            border-right: none;
        }
        .health-score {
            font-size: 2.5em;
            font-weight: 700;
            display: block;
            margin-bottom: 6px;
        }
        .health-label {
            font-size: 0.95em;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: block;
            font-weight: 500;
        }
        .health-good { color: #10b981; }
        .health-warning { color: #f59e0b; }
        .health-critical { color: #ef4444; }

        /* 响应式设计调整 */
        @media (max-width: 768px) {
            .health-summary {
                grid-template-columns: 1fr 1fr;
            }
            .health-card {
                padding: 15px;
                border-bottom: 1px solid #e2e8f0;
            }
            .health-card:nth-child(odd) {
                border-right: 1px solid #e2e8f0;
            }
            .health-card:nth-child(even) {
                border-right: none;
            }
            .health-card:nth-child(3),
            .health-card:nth-child(4) {
                border-bottom: none;
            }
            .health-score {
                font-size: 2em;
            }
        }
        .resource-pools {
            padding: 0 20px 20px;
        }
        .resource-pool {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-top: 20px;
            overflow: hidden;
        }
        .resource-pool-header {
            padding: 12px 15px;
            font-size: 1.1em;
            font-weight: 600;
            color: #fff;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        /* 根据资源类型设置不同的颜色 */
        .resource-header-total {
            background-color: #3b82f6; /* 蓝色 - 总资源 */
        }
        .resource-header-intel {
            background-color: #6366f1; /* 靛蓝色 - Intel节点 */
        }
        .resource-header-arm {
            background-color: #10b981; /* 绿色 - ARM节点 */
        }
        .resource-header-hg {
            background-color: #8b5cf6; /* 紫色 - 海光节点 */
        }
        .resource-header-taint {
            background-color: #a855f7; /* 淡紫色 - 污点节点 */
        }
        .resource-header-common {
            background-color: #0ea5e9; /* 天蓝色 - 通用节点 */
        }
        .resource-header-gpu {
            background-color: #f59e0b; /* 橙色 - GPU节点 */
        }
        .resource-header-aplus {
            background-color: #ec4899; /* 粉色 - A+节点 */
        }
        .resource-header-dplus {
            background-color: #f43f5e; /* 红色 - D+节点 */
        }

        .resource-pool-type {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .node-count-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 0 8px;
            font-size: 0.8em;
            color: white;
            height: 24px;
            margin-left: 8px;
        }
        .node-detail {
            margin-left: 4px;
            font-size: 0.9em;
            opacity: 0.9;
        }
        .resource-pool-info {
            font-size: 0.8em;
            color: rgba(255, 255, 255, 0.9);
            font-weight: normal;
        }
        .resource-usage-stats {
            margin-top: 8px;
            display: flex;
            gap: 12px;
        }
        .usage-stat {
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            padding: 4px 10px;
            display: flex;
            align-items: center;
            font-size: 0.85em;
        }
        .usage-stat-icon {
            margin-right: 6px;
            width: 16px;
            height: 16px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        .usage-stat-label {
            font-weight: 600;
            margin-right: 4px;
        }
        .usage-stat-value {
            opacity: 0.9;
        }
        .usage-warning {
            background-color: rgba(245, 158, 11, 0.3);
        }
        .usage-critical {
            background-color: rgba(239, 68, 68, 0.3);
        }
        .usage-emergency {
            background-color: rgba(220, 38, 38, 0.3);
        }
        .resource-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 10px;
            padding: 15px;
            background-color: white;
        }
        /* 资源池中的健康卡片样式 */
        .resource-grid .health-card {
            background-color: #f8fafc;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            margin: 0;
        }
        .resource-grid .health-score {
            font-size: 2em;
            margin-bottom: 4px;
        }

        /* 工具提示样式 */
        .tooltip {
            position: relative;
            display: inline-block;
            cursor: help;
        }
        /* 为不同位置的工具提示设置不同的样式 */
        .resource-grid .tooltip .tooltip-text {
            top: 105%; /* 跟元素更近，减少空隙 */
            bottom: auto;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.75em; /* 减小字体 */
            padding: 6px 10px; /* 减小内边距 */
        }
        /* 为CPU和内存分配率的悬停框调整位置 */
        .summary-item.tooltip .tooltip-text {
            top: 105%;
            bottom: auto;
            left: 50%;
            transform: translateX(-50%);
            text-align: left;
            width: 250px;
            font-size: 0.75em;
            padding: 6px 10px;
        }
        .resource-pool-type .tooltip .tooltip-text {
            bottom: auto;
            top: 110%; /* 跟元素更近 */
            left: 0;
            transform: none;
            text-align: left;
            font-size: 0.75em;
            padding: 6px 10px;
        }
        .node-type-indicator .tooltip .tooltip-text {
            top: auto;
            bottom: 110%; /* 改为显示在上方而非下方 */
            left: 0;
            transform: none;
            text-align: left;
            width: 250px;
            font-size: 0.75em;
            padding: 6px 10px;
        }
        .node-type-indicator .tooltip .tooltip-text::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 15px;
            border-width: 5px;
            border-style: solid;
            border-color: rgba(0, 0, 0, 0.8) transparent transparent transparent;
        }
        .tooltip .tooltip-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 16px;
            height: 16px;
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            font-size: 10px;
            margin-left: 6px;
            color: white;
        }
        .tooltip .tooltip-text {
            visibility: hidden;
            position: absolute;
            z-index: 1000; /* 增加z-index确保显示在最上层 */
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 8px 12px;
            width: 220px;
            font-size: 0.8em;
            font-weight: normal;
            line-height: 1.4;
            opacity: 0;
            transition: opacity 0.2s, visibility 0.2s;
            pointer-events: none; /* 防止鼠标事件被捕获导致闪烁 */
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* 添加阴影增强可见度 */
        }
        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }
        .node-type-indicator {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 15px;
            padding: 0 15px 15px;
        }
        .node-type-badge {
            background-color: #f1f5f9;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            color: #475569;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        .badge-label {
            font-weight: 600;
            color: #334155;
        }
        /* 资源池概览表格样式 */
        .resource-overview-table {
            margin: 20px auto 30px;
            max-width: 1000px;
            overflow-x: auto;
            padding: 0 5px;
        }
        .resource-overview-table h2 {
            margin-bottom: 15px;
            color: #1e293b;
            font-size: 1.3em;
            font-weight: 600;
            text-align: center;
            padding-bottom: 8px;
            position: relative;
        }
        .resource-overview-table h2:after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(to right, #3b82f6, #93c5fd);
            border-radius: 3px;
        }
        .overview-table {
            width: 100%;
            table-layout: fixed;
            border-collapse: separate;
            border-spacing: 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 30px;
            background-color: #fff;
            font-size: 0.95em; /* 减小字体大小 */
        }
        .overview-table tr {
            height: 38px !important; /* 强制所有行使用相同高度 */
            min-height: 38px !important;
            max-height: 38px !important;
            line-height: normal;
        }
        .overview-table th, .overview-table td {
            padding: 0 6px !important; /* 减小内边距 */
            border-bottom: 1px solid #e2e8f0;
            text-align: center;
            transition: background-color 0.2s;
            overflow: hidden;
            vertical-align: middle; /* 确保内容垂直居中 */
            height: 38px !important; /* 强制所有单元格使用相同高度 */
            min-height: 38px !important;
            max-height: 38px !important;
            line-height: normal;
            box-sizing: border-box;
        }
        .overview-table th {
            background-color: #f1f5f9;
            font-weight: 600;
            color: #1e293b;
            padding: 10px 6px; /* 减小表头内边距 */
            border-bottom: 2px solid #cbd5e1;
            font-size: 0.85em;
            letter-spacing: 0.5px;
            text-transform: uppercase;
        }
        .overview-table tr:last-child td {
            border-bottom: none;
        }
        .overview-table tr:nth-child(even) {
            background-color: #f8fafc;
        }
        .overview-table tr:hover {
            background-color: #f1f5f9;
        }
        .overview-table td:first-child {
            text-align: left;
            font-weight: 600;
            background-color: #f8fafc;
            color: #334155;
            border-right: 1px solid #e2e8f0;
            width: 20%; /* 增加一点宽度以适应集群名称 */
            padding-left: 10px;
            font-size: 0.9em; /* 集群名称字体稍微小一点 */
        }
        .overview-table th:not(:first-child),
        .overview-table td:not(:first-child) {
            width: 20%;
        }
        .resource-indicators {
            display: flex;
            flex-direction: row; /* 水平布局 */
            gap: 0; /* 消除间隙 */
            position: relative;
            max-width: 180px;
            margin: 0 auto;
            justify-content: center;
        }
        .resource-indicator {
            flex: 1;
            padding: 2px 4px !important;
            height: 20px !important;
            min-height: 20px !important;
            max-height: 20px !important;
            line-height: 16px !important;
            position: relative;
            color: rgba(0, 0, 0, 0.8) !important;
            font-weight: 600;
            text-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
            border: none !important;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 0.7em !important;
        }
        /* 左边的CPU指示器圆角样式 */
        .resource-indicator:first-child {
            border-radius: 4px 0 0 4px;
        }
        /* 右边的内存指示器圆角样式 */
        .resource-indicator:last-child {
            border-radius: 0 4px 4px 0;
        }
        .resource-indicator span {
            margin: 0 1px;
        }
        .resource-indicator:hover {
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.12);
            transform: translateY(-1px);
            z-index: 5;
        }
        .resource-indicators:hover {
            z-index: 10;
        }
        /* 颜色类 - 更新为与说明框一致的颜色 */
        .resource-indicator.normal {
            background-color: rgba(74, 222, 128, 0.4) !important; /* 绿色 - 降低不透明度 */
            border: none !important;
        }
        .resource-indicator.warning {
            background-color: rgba(250, 204, 21, 0.4) !important; /* 黄色 - 降低不透明度 */
            border: none !important;
        }
        .resource-indicator.critical {
            background-color: rgba(248, 113, 113, 0.4) !important; /* 红色 - 降低不透明度 */
            border: none !important;
        }
        .resource-indicator.emergency {
            background-color: rgba(239, 68, 68, 0.4) !important; /* 深红色 - 降低不透明度 */
            border: none !important;
        }
        .resource-indicator.underutilized {
            background-color: rgba(147, 197, 253, 0.4) !important; /* 浅蓝色 - 降低不透明度 */
            border: none !important;
        }
        /* 为不同分配率值的单元格设置更符合下方详情的背景色 */
        /* CPU: 85.0%, 内存: 90.0% 紧急/危险 */
        .resource-indicator:has(span:contains("85.0%")), 
        .resource-indicator:has(span:contains("90.0%")) {
            background-color: rgba(239, 68, 68, 0.7); /* 红色背景但更柔和 */
        }

        /* CPU: 70.0%, 内存: 75.0% 警告 */
        .resource-indicator:has(span:contains("70.0%")),
        .resource-indicator:has(span:contains("75.0%")) {
            background-color: rgba(250, 204, 21, 0.7); /* 黄色背景但更柔和 */
        }

        /* CPU: 50.0%, 内存: 50.0% 未充分利用 */
        .resource-indicator:has(span:contains("50.0%")) {
            background-color: rgba(147, 197, 253, 0.7); /* 蓝色背景但更柔和 */
        }

        /* CPU: 25.0%, 内存: 25.0% 严重未充分利用 */
        .resource-indicator:has(span:contains("25.0%")) {
            background-color: rgba(147, 197, 253, 0.6); /* 更浅的蓝色背景 */
        }

        /* 修改单元格内文字颜色，确保在透明背景下更清晰 */
        .resource-indicator {
            color: #1a202c;
            font-weight: 600;
            text-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
        }
        /* 悬停卡片样式 */
        .resource-card-popup {
            display: none;
            position: absolute;
            z-index: 1000;
            width: 220px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 12px;
            top: 30px;
            left: 50%;
            transform: translateX(-50%);
            text-align: left;
            color: #334155;
            border: 1px solid #e2e8f0;
            animation: fadeIn 0.2s ease-out;
            font-size: 0.85em;
        }

        .resource-card-popup h4 {
            margin: 0 0 8px 0;
            color: #1e293b;
            font-size: 1em;
            border-bottom: 1px solid #e2e8f0;
            padding-bottom: 6px;
        }

        .resource-card-popup p {
            margin: 4px 0;
            line-height: 1.4;
            font-size: 0.9em;
        }

        .resource-card-popup strong {
            color: #475569;
            font-weight: 600;
            margin-right: 4px;
        }

        /* 确保资源指示器相对定位，以便悬停卡片基于它定位 */
        .resource-indicators {
            position: relative;
        }

        /* 当父级悬停时显示卡片 */
        .resource-indicators:hover .resource-card-popup {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translate(-50%, -5px); }
            to { opacity: 1; transform: translate(-50%, 0); }
        }
        .no-data {
            color: #94a3b8;
            font-style: italic;
            font-size: 0.75em !important;
            padding: 4px 8px !important;
            text-align: center;
            background-color: #f8fafc;
            border-radius: 4px;
            border: 1px dashed #cbd5e1;
            margin: 0 auto;
            max-width: 80%;
            height: 20px !important; /* 与指示器高度一致 */
            min-height: 20px !important;
            max-height: 20px !important;
            display: flex;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
        }
        /* 资源分配率说明样式 */
        .threshold-indicators {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #e2e8f0;
        }
        .threshold-title {
            display: block;
            font-weight: 600;
            margin-bottom: 10px;
            color: #334155;
            font-size: 0.95em;
        }
        .threshold-group {
            margin-bottom: 10px;
            background-color: #f8fafc;
            border-radius: 6px;
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            display: inline-block;
            margin-right: 10px;
            vertical-align: top;
            width: 100%; /* 使用100%宽度替代50%减去边距 */
            box-sizing: border-box;
        }
        .threshold-group:last-child {
            margin-right: 0;
        }
        .threshold-group-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #4a5568;
            font-size: 0.9em;
        }
        .threshold-legend {
            display: flex;
            flex-wrap: nowrap; /* 确保不换行 */
            gap: 4px; /* 减少间距 */
            margin: 6px 0;
            overflow-x: visible; /* 不需要滚动条 */
            white-space: nowrap; /* 文本不换行 */
            width: 100%;
        }
        .threshold-item {
            display: flex;
            align-items: center;
            gap: 3px; /* 减少内部间距 */
            background-color: white;
            padding: 3px 6px; /* 减少内边距 */
            border-radius: 4px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            font-size: 0.75em; /* 稍微减小字体 */
            margin-bottom: 4px;
            white-space: nowrap; /* 文本不换行 */
            flex-shrink: 0; /* 防止收缩 */
        }
        .threshold-color {
            width: 10px;
            height: 10px;
            border-radius: 2px;
            flex-shrink: 0;
        }
        .threshold-color.underutilized {
            background-color: #93c5fd; /* 浅蓝色 - 低分配率 */
        }
        .threshold-color.normal {
            background-color: #4ade80; /* 绿色 - 正常 */
        }
        .threshold-color.warning {
            background-color: #facc15; /* 黄色 - 警告 */
        }
        .threshold-color.critical {
            background-color: #f87171; /* 橙红色 - 危险 */
        }
        .threshold-color.emergency {
            background-color: #ef4444; /* 红色 - 紧急 */
        }
        .footer {
            padding: 15px 30px;
            background-color: #f8fafc;
            border-top: 1px solid #e2e8f0;
            font-size: 0.85em;
            color: #64748b;
            text-align: center;
        }
        .note-box {
            background-color: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 8px;
            padding: 12px 15px;
            margin: 15px 0;
            color: #0369a1;
            font-size: 0.9em;
            line-height: 1.4;
        }
        .note-box-title {
            font-weight: 600;
            display: block;
            margin-bottom: 4px;
            font-size: 0.95em;
        }
        .warning-alert {
            background-color: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 15px 20px;
            margin: 20px 0;
            color: #991b1b;
            font-size: 0.95em;
            line-height: 1.5;
        }
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 0;
                max-width: 100%;
            }
            .content {
                padding: 15px;
            }
            .header {
                padding: 15px 20px;
            }
            .header-title {
                font-size: 1.3em;
            }
            .cluster-header {
                font-size: 1.1em;
                padding: 12px 15px;
                flex-direction: column;
                align-items: flex-start;
            }
            .cluster-header-info {
                margin-top: 5px;
            }
            /* 优化资源概览表格适配小屏幕 */
            .overview-table {
                font-size: 0.8em;
                min-width: 100%;
                border-radius: 8px;
                overflow-x: auto;
                display: block;
            }
            .overview-table th, .overview-table td {
                padding: 4px !important;
                height: auto !important;
                min-height: auto !important;
                max-height: none !important;
            }
            .overview-table td:first-child {
                max-width: 120px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            /* 调整资源指示器在移动端的大小 */
            .resource-indicators {
                max-width: 120px;
            }
            .resource-indicator {
                padding: 1px 2px !important;
                font-size: 0.65em !important;
                height: 16px !important;
                min-height: 16px !important;
                max-height: 16px !important;
                line-height: 14px !important;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        @media (max-width: 480px) {
            /* 现有样式 */
            .cluster-summary {
                grid-template-columns: 1fr;
            }
            .resource-grid {
                grid-template-columns: 1fr;
            }
            /* 更小屏幕特定优化 */
            .overview-table {
                font-size: 0.7em;
            }
            .threshold-item {
                font-size: 0.7em;
                padding: 2px 4px;
            }
            /* 调整阈值指示器布局 */
            .threshold-legend {
                flex-wrap: wrap;
                justify-content: center;
            }
            /* 优化集群标题显示 */
            .cluster-header {
                font-size: 1em;
                padding: 10px;
            }
            .cluster-header-info {
                font-size: 0.75em;
            }
            /* 进一步调整资源指示器 */
            .resource-indicators {
                max-width: 100px;
            }
            .resource-indicator {
                font-size: 0.6em !important;
                height: 14px !important;
                min-height: 14px !important;
                max-height: 14px !important;
                line-height: 12px !important;
            }
            /* 确保所有提示框在小屏幕上正确定位 */
            .tooltip .tooltip-text {
                max-width: 80vw;
                left: 10px;
                right: 10px;
                transform: none;
                font-size: 0.65em;
                width: auto;
            }
        }

        /* 添加额外的超小屏幕支持 */
        @media (max-width: 360px) {
            .container {
                margin: 5px;
            }
            .content {
                padding: 10px;
            }
            .header-title {
                font-size: 1.1em;
            }
            .overview-table {
                font-size: 0.65em;
            }
            /* 在极小屏幕上进一步简化表格 */
            .overview-table th:nth-child(n+4), 
            .overview-table td:nth-child(n+4) {
                display: none; /* 隐藏不太重要的列 */
            }
            .resource-indicators {
                max-width: 90px;
            }
        }
        /* 表格控制和分页样式 */
        .table-controls {
            display: none; /* 直接隐藏整个控制区域 */
        }
        .search-box {
            display: none;
        }
        .table-pagination {
            display: none;
        }
        .pagination-info {
            font-size: 0.85em;
            color: #64748b;
        }
        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .pagination-controls button {
            background-color: #f1f5f9;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 0.85em;
            color: #334155;
            cursor: pointer;
            transition: all 0.2s;
        }
        .pagination-controls button:hover:not(:disabled) {
            background-color: #e2e8f0;
        }
        .pagination-controls button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        @media (max-width: 768px) {
            .table-controls {
                flex-direction: row;
                padding: 8px;
            }
            .search-box {
                width: 100%;
            }
        }

        /* JavaScript 添加的类 */
        .hidden {
            display: none;
        }

        /* 移除:has选择器，使用更通用的样式方法 */
        /* 为资源指示器添加轻柔的背景样式 */
        .resource-indicator {
            color: #1a202c !important; /* 深色文字，确保在透明背景上清晰可见 */
            font-weight: 600;
            text-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
            background-color: rgba(255, 255, 255, 0.2); /* 默认背景 */
            border: none !important; /* 移除边框 */
            border-radius: 3px; /* 更小的圆角 */
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06); /* 轻微阴影，增加深度感 */
        }

        .resource-indicator.normal {
            background-color: rgba(74, 222, 128, 0.75) !important; /* 绿色 - 正常，更轻柔 */
            border: none !important;
        }
        .resource-indicator.warning {
            background-color: rgba(250, 204, 21, 0.75) !important; /* 黄色 - 警告，更轻柔 */
            border: none !important;
        }
        .resource-indicator.critical {
            background-color: rgba(248, 113, 113, 0.75) !important; /* 红色 - 危险，更轻柔 */
            border: none !important;
        }
        .resource-indicator.emergency {
            background-color: rgba(239, 68, 68, 0.75) !important; /* 深红色 - 紧急，更轻柔 */
            border: none !important;
        }
        .resource-indicator.underutilized {
            background-color: rgba(147, 197, 253, 0.75) !important; /* 浅蓝色 - 低分配率，更轻柔 */
            border: none !important;
        }

        /* 自定义样式，用于不同的分配率值 */
        .cpu-85, .mem-90 {
            background-color: rgba(239, 68, 68, 0.35) !important; /* 红色背景但更轻柔 */
            border: none !important;
        }

        .cpu-70, .mem-75 {
            background-color: rgba(250, 204, 21, 0.35) !important; /* 黄色背景但更轻柔 */
            border: none !important;
        }

        .cpu-50, .mem-50 {
            background-color: rgba(147, 197, 253, 0.35) !important; /* 蓝色背景但更轻柔 */
            border: none !important;
        }

        .cpu-25, .mem-25 {
            background-color: rgba(147, 197, 253, 0.3) !important; /* 更浅的蓝色背景 */
            border: none !important;
        }

        /* 返回顶部按钮样式 */
        .back-to-top {
            position: fixed;
            right: 25px;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            line-height: 40px;
            background: #4a5568;
            color: white;
            text-align: center;
            border-radius: 50%;
            text-decoration: none;
            font-size: 20px;
            opacity: 0.7;
            z-index: 1000;
            transition: opacity 0.3s, transform 0.3s;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        .back-to-top:hover {
            opacity: 1;
            transform: translateY(-50%) scale(1.1);
        }
        
        /* 异常集群链接样式 */
        .abnormal-cluster-link {
            color: #ef4444;
            text-decoration: none;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
        }
        
        .abnormal-cluster-link:hover {
            text-decoration: underline;
        }
        
        .abnormal-cluster-icon {
            display: inline-block;
            margin-right: 4px;
            font-size: 0.9em;
        }
        
        /* 集群ID锚点样式 */
        .cluster-anchor {
            display: block;
            position: relative;
            top: -70px;
            visibility: hidden;
        }
        /* 修复集群展示区域样式以提高可读性 */
        .resource-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 10px;
            padding: 15px;
            background-color: white;
        }
        
        /* 增加资源指标对比度 */
        .summary-value.warning {
            color: #854d0e; /* 深黄色 - 更好的对比度 */
            font-weight: 700;
        }

        /* 使用率样式类 */
        .width-0 { width: 0%; }
        .width-5 { width: 5%; }
        .width-10 { width: 10%; }
        .width-15 { width: 15%; }
        .width-20 { width: 20%; }
        .width-25 { width: 25%; }
        .width-30 { width: 30%; }
        .width-35 { width: 35%; }
        .width-40 { width: 40%; }
        .width-45 { width: 45%; }
        .width-50 { width: 50%; }
        .width-55 { width: 55%; }
        .width-60 { width: 60%; }
        .width-65 { width: 65%; }
        .width-70 { width: 70%; }
        .width-75 { width: 75%; }
        .width-80 { width: 80%; }
        .width-85 { width: 85%; }
        .width-90 { width: 90%; }
        .width-95 { width: 95%; }
        .width-100 { width: 100%; }

        /* 新增: 集群组样式 */
        .group-header {
            background-color: #f1f5f9;
        }
        
        .group-name {
            font-weight: bold;
            padding: 8px 12px;
            text-align: left;
            background-color: #e2e8f0;
            color: #334155;
            font-size: 16px;
            border-radius: 4px 4px 0 0;
            border-bottom: 2px solid #cbd5e1;
        }
        
        /* 资源指示器样式 */
        .resource-indicators {
            display: flex;
            flex-direction: row; /* 水平布局 */
            gap: 0; /* 消除间隙 */
            position: relative;
            max-width: 180px;
            margin: 0 auto;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="container">
        
        <div class="header">
            <h1 class="header-title">K8S 集群资源报告</h1>
            <p class="header-date">报告日期: {{ .ReportDate }}</p>
        </div>
        <div class="content">
            {{ if not .Clusters }}
                <p>今日无可用资源数据。</p>
            {{ else }}

                <!-- 集群健康概览 -->
                <div class="health-summary">
                    <div class="health-card">
                        <span class="health-score">{{ .Stats.TotalClusters }}</span>
                        <span class="health-label">总已巡检集群数</span>
                    </div>
                    <div class="health-card">
                        <span class="health-score health-good">{{ .Stats.NormalClusters }}</span>
                        <span class="health-label">正常集群数</span>
                    </div>
                    <div class="health-card">
                        <span class="health-score {{ if gt .Stats.AbnormalClusters 0 }}health-critical{{ else }}health-good{{ end }}">{{ .Stats.AbnormalClusters }}</span>
                        <span class="health-label">异常集群数</span>
                    </div>
                    <div class="health-card">
                        <span class="health-score">{{ printf "%.2f" .Stats.GeneralPodDensity }}</span>
                        <span class="health-label">通用集群Pod密度</span>
                    </div>
                </div>

                <div class="note-box">
                    <span class="note-box-title">📊 报告说明</span>
                    {{ if eq .Environment "test" }}
                    <p style="margin: 0 0 8px 0; font-size: 0.9em;">此报告显示<strong>测试环境</strong>资源分配率异常的集群与资源池，以便重点关注潜在风险区域。测试环境低利用率不计为异常，高利用率阈值比生产环境高5%。完整数据已附加在邮件中的Excel文件。</p>
                    {{ else }}
                    <p style="margin: 0 0 8px 0; font-size: 0.9em;">此报告显示<strong>生产环境</strong>资源分配率异常（低于55%或高于阈值）的集群与资源池，以便重点关注潜在风险区域。完整数据已附加在邮件中的Excel文件。</p>
                    {{ end }}
                    <p style="margin: 0; font-size: 0.9em;">资源数据仅计算物理机节点，虚拟机节点仅计算节点数量，无物理节点集群，不纳入统计。</p>

                    <div class="threshold-indicators">
                        <span class="threshold-title">资源分配率说明：</span>

                        {{ if eq .Environment "test" }}
                        <!-- 测试环境阈值 -->
                        <div class="threshold-group">
                            <div class="threshold-group-title">物理机节点数 ≤ 150台（测试环境）：</div>
                            <div class="threshold-legend">
                                <div class="threshold-item"><div class="threshold-color normal"></div> <span>正常 (< {{ getThresholdWarning "test" "small" }}%)</span></div>
                                <div class="threshold-item"><div class="threshold-color warning"></div> <span>警告 ({{ getThresholdWarning "test" "small" }}% - {{ getThresholdCritical "test" "small" }}%)</span></div>
                                <div class="threshold-item"><div class="threshold-color critical"></div> <span>危险 ({{ getThresholdCritical "test" "small" }}% - {{ getThresholdEmergency }}%)</span></div>
                                <div class="threshold-item"><div class="threshold-color emergency"></div> <span>紧急 (> {{ getThresholdEmergency }}%)</span></div>
                            </div>
                        </div>

                        <div class="threshold-group">
                            <div class="threshold-group-title">物理机节点数 > 150台（测试环境）：</div>
                            <div class="threshold-legend">
                                <div class="threshold-item"><div class="threshold-color normal"></div> <span>正常 (< {{ getThresholdWarning "test" "large" }}%)</span></div>
                                <div class="threshold-item"><div class="threshold-color warning"></div> <span>警告 ({{ getThresholdWarning "test" "large" }}% - {{ getThresholdCritical "test" "large" }}%)</span></div>
                                <div class="threshold-item"><div class="threshold-color critical"></div> <span>危险 ({{ getThresholdCritical "test" "large" }}% - {{ getThresholdEmergency }}%)</span></div>
                                <div class="threshold-item"><div class="threshold-color emergency"></div> <span>紧急 (> {{ getThresholdEmergency }}%)</span></div>
                            </div>
                        </div>
                        {{ else }}
                        <!-- 生产环境阈值 -->
                        <div class="threshold-group">
                            <div class="threshold-group-title">物理机节点数 ≤ 150台（生产环境）：</div>
                            <div class="threshold-legend">
                                <div class="threshold-item"><div class="threshold-color underutilized"></div> <span>低分配率 (< {{ getThresholdLow }}%)</span></div>
                                <div class="threshold-item"><div class="threshold-color normal"></div> <span>正常 ({{ getThresholdLow }}% - {{ getThresholdWarning "prd" "small" }}%)</span></div>
                                <div class="threshold-item"><div class="threshold-color warning"></div> <span>警告 ({{ getThresholdWarning "prd" "small" }}% - {{ getThresholdCritical "prd" "small" }}%)</span></div>
                                <div class="threshold-item"><div class="threshold-color critical"></div> <span>危险 ({{ getThresholdCritical "prd" "small" }}% - {{ getThresholdEmergency }}%)</span></div>
                                <div class="threshold-item"><div class="threshold-color emergency"></div> <span>紧急 (> {{ getThresholdEmergency }}%)</span></div>
                            </div>
                        </div>

                        <div class="threshold-group">
                            <div class="threshold-group-title">物理机节点数 > 150台（生产环境）：</div>
                            <div class="threshold-legend">
                                <div class="threshold-item"><div class="threshold-color underutilized"></div> <span>低分配率 (< {{ getThresholdLow }}%)</span></div>
                                <div class="threshold-item"><div class="threshold-color normal"></div> <span>正常 ({{ getThresholdLow }}% - {{ getThresholdWarning "prd" "large" }}%)</span></div>
                                <div class="threshold-item"><div class="threshold-color warning"></div> <span>警告 ({{ getThresholdWarning "prd" "large" }}% - {{ getThresholdCritical "prd" "large" }}%)</span></div>
                                <div class="threshold-item"><div class="threshold-color critical"></div> <span>危险 ({{ getThresholdCritical "prd" "large" }}% - {{ getThresholdEmergency }}%)</span></div>
                                <div class="threshold-item"><div class="threshold-color emergency"></div> <span>紧急 (> {{ getThresholdEmergency }}%)</span></div>
                            </div>
                        </div>
                        {{ end }}
                    </div>
                </div>

                <!-- 资源池概览表格 -->
                <div class="resource-overview-table">
                    <a id="top"></a>
                    <table class="overview-table" id="clusterTable">
                        <thead>
                            <tr>
                                <th>集群名称</th>
                                <th>
                                    通用资源<br>
                                    <span style="font-size: 0.8em; font-weight: normal; color: #64748b; text-transform: lowercase;">total_common</span>
                                </th>
                                <th>
                                    Intel通用<br>
                                    <span style="font-size: 0.8em; font-weight: normal; color: #64748b; text-transform: lowercase;">intel_common</span>
                                </th>
                                <th>
                                    海光通用<br>
                                    <span style="font-size: 0.8em; font-weight: normal; color: #64748b; text-transform: lowercase;">hg_common</span>
                                </th>
                                <th>
                                    ARM通用<br>
                                    <span style="font-size: 0.8em; font-weight: normal; color: #64748b; text-transform: lowercase;">arm_common</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {{ $currentGroup := "" }}
                            {{ range .Clusters }}
                            {{ $cluster := . }}
                            
                            {{ if ne $currentGroup .Desc }}
                                {{ $currentGroup = .Desc }}
                                <tr class="group-header">
                                    <td colspan="5" class="group-name">{{ $currentGroup }}</td>
                                </tr>
                            {{ end }}
                            
                            <tr class="cluster-row">
                                <td>
                                    {{ if .IsAbnormalOverall }}
                                        <a href="#cluster-{{ .ClusterName }}" class="abnormal-cluster-link">
                                            <span class="abnormal-cluster-icon">⚠️</span>{{ .ClusterName }}
                                        </a>
                                    {{ else }}
                                        {{ .ClusterName }}
                                    {{ end }}
                                </td>
                                <!-- 为每种资源池类型创建单元格 -->
                                {{ range .OverviewPools }} {{/* Iterate over pre-filtered and structured pools for the overview */}}
                                    <td>
                                        {{ if .HasData }}
                                            <div class="resource-indicators">
                                                <div class="resource-indicator {{ .CPUIndicatorClass }}">{{ .CPUUsageText }}</div>
                                                <div class="resource-indicator {{ .MemoryIndicatorClass }}">{{ .MemoryUsageText }}</div>
                                                <div class="resource-card-popup">
                                                    <h4>{{ .NodeType }} ({{ .ResourceType }})</h4>
                                                    <p><strong>CPU分配率:</strong> {{ .CPUUsageText }} ({{ .CPURequestFormatted }}/{{ .CPUCapacityFormatted }} 核)</p>
                                                    <p><strong>内存分配率:</strong> {{ .MemoryUsageText }} ({{ .MemoryRequestFormatted }}/{{ .MemoryCapacityFormatted }} GiB)</p>
                                                    <p><strong>节点数:</strong> {{ .Nodes }} 个 ({{ .BMCount }} 物理机)</p>
                                                    {{ if and ($.ShowResourcePoolDesc) (.Desc) }}
                                                    <p class="resource-description"><strong>描述:</strong> {{ .Desc }}</p>
                                                    {{ end }}
                                                </div>
                                            </div>
                                        {{ else }}
                                            <div class="no-data">无资源</div>
                                        {{ end }}
                                    </td>
                                {{ end }}
                            </tr>
                            {{ end }}
                        </tbody>
                    </table>
                </div>

                {{ range .Clusters }}
                    {{/* $clusterHasAbnormalUsage is now .ShowDetails in Go backend */}}
                    {{ if .ShowDetails }}
                    <div class="cluster-section">
                        <a id="cluster-{{ .ClusterName }}" class="cluster-anchor"></a>
                        <div class="cluster-header">
                            <span>集群: {{ .ClusterName }} （{{ .Desc }}）</span>
                        </div>

                        <div class="resource-pools">
                            {{ range .ResourcePools }}
                                {{ if .ShowPoolDetails }}
                                <div class="resource-pool">
                                    <div class="resource-pool-header {{ .HeaderClass }}">
                                        <div class="resource-pool-type">
                                            <span class="tooltip">
                                                {{ .NodeType }} ({{ .ResourceType }})
                                                <span class="tooltip-icon">?</span>
                                                <span class="tooltip-text">
                                                    {{ if .TooltipText }}
                                                        {{ .TooltipText }}
                                                    {{ else if eq .ResourceType "total" }}
                                                        集群所有物理机资源总和，包含集群中所有类型的节点。
                                                    {{ else if eq .ResourceType "intel_common" }}
                                                        Intel物理机通用应用节点资源，没有特殊标记或污点的Intel节点。
                                                    {{ else if eq .ResourceType "arm_common" }}
                                                        ARM物理机节点通用应用资源，没有特殊标记或污点的ARM节点。
                                                    {{ else }}
                                                        {{ .NodeType }}资源池，类型: {{ .ResourceType }}
                                                    {{ end }}
                                                </span>
                                            </span>
                                            <span class="node-count-badge">{{ .Nodes }} 节点 <span class="node-detail">(物理机: {{ .BMCount }}, 虚拟机: {{ .VMCount }})</span></span>
                                        </div>
                                    </div>
                                    <div class="resource-grid">
                                        <div class="health-card tooltip">
                                            <span class="health-score">
                                                {{ if gt .BMCount 0 }}
                                                    {{ printf "%.1f" (div (float64 .PodCount) (float64 .BMCount)) }}
                                                {{ else }}
                                                    0
                                                {{ end }}
                                            </span>
                                            <span class="health-label">Pod密度</span>
                                            <span class="tooltip-text">
                                                Pod密度表示每台物理机上运行的Pod数量。计算方式：Pod总数 / 物理机数量。该指标可以反映集群的资源利用效率。
                                            </span>
                                        </div>
                                        {{ if .HasCPUStats }}
                                            <div class="summary-item tooltip {{ .CPUDisplay.Class }}">
                                                <span class="summary-label">CPU 分配率 ({{ .CPURequestFormatted }}/{{ .CPUCapacityFormatted }} 核)</span>
                                                <span class="summary-value {{ .CPUDisplay.Class }}">
                                                    {{ .CPUDisplay.UsageText }}
                                                </span>
                                                <span class="tooltip-text">
                                                    {{ .CPUDisplay.TooltipText | safeHTML }}
                                                </span>
                                                <div class="usage-bar-container">
                                                    <div class="usage-bar {{ .CPUDisplay.BarClass }} {{ .CPUDisplay.BarWidthClass }}"></div>
                                                </div>

                                                <!-- CPU分配率波动图 -->
                                                {{ if .CPUDisplay.History }}
                                                <table class="trend-container" cellspacing="0" cellpadding="0">
                                                    <tr>
                                                        {{ range .CPUDisplay.History }}
                                                            <td class="trend-day {{ .Class }}" title="{{ .Label }} CPU分配率: {{ .ValueText }}"></td>
                                                        {{ end }}
                                                    </tr>
                                                </table>
                                                <span class="trend-label">7天波动</span>
                                                {{ end }}
                                            </div>
                                        {{ end }}

                                        {{ if gt .MemoryCapacity 0.0 }}
                                            {{ $memUsagePerc := 0.0 }}
                                            {{ if gt .MemoryCapacity 0.0 }}
                                                {{ $memUsagePerc = div .MemoryRequest .MemoryCapacity | mul 100.0 }}
                                            {{ end }}
                                            
                                            <!-- 使用后端已实现的函数获取内存样式 -->
                                            {{ $memClass := getMemStyleClass .BMCount $memUsagePerc $.Environment }}
                                            
                                            <div class="summary-item tooltip {{ $memClass }}">
                                                <span class="summary-label">内存分配率 ({{ printf "%.1f" .MemoryRequest }}/{{ printf "%.1f" .MemoryCapacity }} GiB)</span>
                                                <span class="summary-value {{ $memClass }}">
                                                    {{ printf "%.1f%%" $memUsagePerc }}
                                                </span>
                                                <span class="tooltip-text">
                                                    {{ getMemTooltipMessage $memUsagePerc .BMCount $.Environment .MemoryRequest .MemoryCapacity | safeHTML }}
                                                </span>
                                                
                                                <!-- 使用后端函数获取内存使用条的样式 -->
                                                {{ $barClass := getBarClassName (getMemoryStyle .BMCount $memUsagePerc $.Environment) }}
                                                {{ $widthClass := getBarWidthClassName $memUsagePerc }}
                                                
                                                <div class="usage-bar-container">
                                                    <div class="usage-bar {{ $barClass }} {{ $widthClass }}"></div>
                                                </div>

                                                <!-- 内存分配率波动图 -->
                                                {{ if .MemoryHistory }}
                                                <table class="trend-container" cellspacing="0" cellpadding="0">
                                                    <tr>
                                                        {{ $pool := . }}
                                                        {{ range $i, $value := .MemoryHistory }}
                                                            {{ $height := $value }}
                                                            {{ if gt $height 100.0 }}{{ $height = 100.0 }}{{ end }}
                                                            {{ $dayLabel := "" }}
                                                            {{ if eq $i 6 }}
                                                                {{ $dayLabel = "今天" }}
                                                            {{ else if eq $i 5 }}
                                                                {{ $dayLabel = "昨天" }}
                                                            {{ else }}
                                                                {{ $daysAgo := sub 6 $i }}
                                                                {{ $dayLabel = printf "%d天前" $daysAgo }}
                                                            {{ end }}
                                                            
                                                            <!-- 使用后端函数获取内存趋势样式类 -->
                                                            {{ $memTrendClass := getMemTrendStyleClass $height $pool.BMCount $.Environment }}
                                                            <td class="trend-day {{ $memTrendClass }}" 
                                                                title="{{ $dayLabel }} 内存分配率: {{ printf "%.1f%%" $height }}"></td>
                                                        {{ end }}
                                                    </tr>
                                                </table>
                                                <span class="trend-label">7天波动</span>
                                                {{ end }}
                                            </div>
                                        {{ end }}
                                    </div>

                                    <div class="node-type-indicator">
                                        <div class="node-type-badge tooltip">
                                            <span>Pod数量:</span>
                                            <span class="badge-label">{{ .PodCount }}</span>
                                            <span class="tooltip-text">按天分布取过去一个月中某天最大的已发布pod数（不区分在线状态）</span>
                                        </div>
                                        <div class="node-type-badge tooltip">
                                            <span>平均CPU:</span>
                                            <span class="badge-label">{{ printf "%.1f核" .PerNodeCpuRequest }}</span>
                                            <span class="tooltip-text">已分配的cpu/物理机节点，即每节点平均分配cpu</span>
                                        </div>
                                        <div class="node-type-badge tooltip">
                                            <span>平均内存:</span>
                                            <span class="badge-label">{{ printf "%.1fGiB" .PerNodeMemRequest }}</span>
                                            <span class="tooltip-text">已分配的内存/物理机节点，即每节点平均分配内存</span>
                                        </div>
                                        <div class="node-type-badge tooltip">
                                            <span>CPU最大使用率:</span>
                                            <span class="badge-label">{{ printf "%.1f%%" (mul .MaxCpuUsageRatio 100) }}</span>
                                            <span class="tooltip-text">过去24小时内CPU平均最大使用率</span>
                                        </div>
                                        <div class="node-type-badge tooltip">
                                            <span>内存最大使用率:</span>
                                            <span class="badge-label">{{ printf "%.1f%%" (mul .MaxMemoryUsageRatio 100) }}</span>
                                            <span class="tooltip-text">过去24小时内内存平均最大使用率</span>
                                        </div>
                                    </div>
                                </div>
                                {{ end }}
                            {{ end }}
                        </div>
                    </div>
                    {{ end }}
                {{ end }}

                {{ if not .HasHighUsageClusters }}
                    <div class="note-box" style="background-color: #f0fff4; border-color: #c6f6d5; color: #2f855a;">
                        <span class="note-box-title">✅ 资源状态良好</span>
                        {{ if eq .Environment "test" }}
                            <p>当前所有集群的资源分配率均在正常范围内（低于测试环境阈值），未发现需要特别关注的情况。完整数据请查看附件中的Excel表格。</p>
                        {{ else }}
                            <p>当前所有集群的资源分配率均在正常范围内（55%-70%之间），未发现需要特别关注的情况。完整数据请查看附件中的Excel表格。</p>
                        {{ end }}
                    </div>
                {{ end }}
            {{ end }}
        </div>
        <div class="footer">
            此邮件为系统自动生成，完整数据请查看附件Excel文件。如有问题请联系运维团队。
        </div>
    </div>
    <a href="#top" style="display: block; position: fixed; top: 50%; right: 20px; transform: translateY(-50%); width: 36px; height: 36px; background-color: #2196F3; color: white; text-align: center; border-radius: 50%; text-decoration: none; box-shadow: 0 2px 5px rgba(0,0,0,0.2); line-height: 36px; font-size: 20px; opacity: 0.8;">↑</a>
</body>
</html>



