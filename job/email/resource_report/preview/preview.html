<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Kubernetes 集群资源报告 - 2025-05-12</title>
    <style type="text/css">
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 
                'Open Sans', 'Helvetica Neue', "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            background-color: #f5f5f5;
            font-size: 14px;
            line-height: 1.5;
        }
        .container {
            max-width: 960px;
            margin: 20px auto;
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 24px rgba(0,0,0,0.06);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
            color: white;
            padding: 24px 30px;
            border-bottom: 1px solid #e2e8f0;
        }
        .header-title {
            font-size: 1.7em;
            font-weight: 600;
            margin: 0;
        }
        .header-date {
            font-size: 0.95em;
            opacity: 0.9;
            margin-top: 5px;
        }
        .content {
            padding: 30px;
        }
        .cluster-section {
            margin-bottom: 35px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
        }
        .cluster-header {
            background-color: #f1f5f9;
            padding: 15px 20px;
            font-size: 1.3em;
            font-weight: 600;
            color: #1e293b;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .cluster-header-info {
            font-size: 0.8em;
            font-weight: normal;
            color: #64748b;
        }
        .cluster-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            padding: 20px;
            background-color: #ffffff;
        }
        .summary-item {
            background-color: #f8fafc;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            border: 1px solid #e2e8f0;
        }
        .summary-label {
            font-size: 0.9em;
            color: #64748b;
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        .summary-value {
            font-size: 1.4em;
            font-weight: 600;
            color: #1e293b;
        }
        .usage-bar-container {
            width: 100%;
            background-color: #f0f0f0;
            height: 10px;
            border-radius: 5px;
            margin: 5px 0;
            overflow: hidden;
        }
        .usage-bar {
            height: 100%;
            border-radius: 5px;
            background-color: #4caf50;
            transition: width 0.3s;
        }
        .usage-bar.underutilized-usage {
            background-color: #93c5fd;  
        }
        .usage-bar.high-usage {
            background-color: #facc15;  
        }
        .usage-bar.danger-usage {
            background-color: #f87171;  
        }
        .usage-bar.emergency-usage {
            background-color: #ef4444;  
        }
        
         
        .usage-bar.normal-usage {
            background-color: #4ade80;  
        }

         
        .trend-container {
            margin-top: 8px;
            width: 100%;
            border-collapse: collapse;
            border-radius: 4px;
            overflow: hidden;
        }
        .trend-container td {
            height: 15px;
            padding: 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .trend-day {
            width: 14.28%;  
        }
        .trend-label {
            font-size: 10px;
            color: #718096;
            text-align: right;
            margin-top: 2px;
            display: block;
        }
        
         
        .trend-day.bar-underutilized {
            background-color: #93c5fd;  
        }
        .trend-day.bar-normal {
            background-color: #4ade80;  
        }
        .trend-day.bar-warning {
            background-color: #facc15;  
        }
        .trend-day.bar-critical {
            background-color: #f87171;  
        }
        .trend-day.bar-emergency {
            background-color: #ef4444;  
        }
        
         
        .cpu-trend-underutilized {
            background-color: rgba(147, 197, 253, 0.5);  
        }
        .cpu-trend-normal {
            background-color: rgba(74, 222, 128, 0.4);  
        }
        .cpu-trend-high {
            background-color: rgba(250, 204, 21, 0.6);  
        }
        .cpu-trend-critical {
            background-color: rgba(248, 113, 113, 0.7);  
        }
        .cpu-trend-emergency {
            background-color: rgba(239, 68, 68, 0.8);  
        }
         
        .memory-trend-underutilized {
            background-color: rgba(147, 197, 253, 0.5);  
        }
        .memory-trend-normal {
            background-color: rgba(74, 222, 128, 0.4);  
        }
        .memory-trend-high {
            background-color: rgba(250, 204, 21, 0.6);  
        }
        .memory-trend-critical {
            background-color: rgba(248, 113, 113, 0.7);  
        }
        .memory-trend-emergency {
            background-color: rgba(239, 68, 68, 0.8);  
        }
        .high-usage {
            background-color: #facc15;  
        }
        .danger-usage {
            background-color: #f87171;  
        }
        .emergency-usage {
            background-color: #ef4444;  
        }
        .summary-item.underutilized {
            background-color: #dbeafe;
            border: 1px solid #93c5fd;
        }
        .summary-item.warning {
            background-color: #fef9c3;
            border: 1px solid #facc15;
        }
        .summary-item.critical {
            background-color: #fee2e2;
            border: 1px solid #f87171;
        }
        .summary-item.emergency {
            background-color: #fecaca;
            border: 1px solid #ef4444;
        }
        .summary-value.underutilized {
            color: #1e40af;  
        }
        .summary-value.warning {
            color: #854d0e;  
        }
        .summary-value.critical {
            color: #991b1b;  
        }
        .summary-value.emergency {
            color: #7f1d1d;  
        }

         
        .health-summary {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            border-bottom: 1px solid #e2e8f0;
            margin-bottom: 20px;
        }
        .health-card {
            padding: 20px 24px;
            text-align: center;
            border-right: 1px solid #e2e8f0;
        }
        .health-card:last-child {
            border-right: none;
        }
        .health-score {
            font-size: 2.5em;
            font-weight: 700;
            display: block;
            margin-bottom: 6px;
        }
        .health-label {
            font-size: 0.95em;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: block;
            font-weight: 500;
        }
        .health-good { color: #10b981; }
        .health-warning { color: #f59e0b; }
        .health-critical { color: #ef4444; }

         
        @media (max-width: 768px) {
            .health-summary {
                grid-template-columns: 1fr 1fr;
            }
            .health-card {
                padding: 15px;
                border-bottom: 1px solid #e2e8f0;
            }
            .health-card:nth-child(odd) {
                border-right: 1px solid #e2e8f0;
            }
            .health-card:nth-child(even) {
                border-right: none;
            }
            .health-card:nth-child(3),
            .health-card:nth-child(4) {
                border-bottom: none;
            }
            .health-score {
                font-size: 2em;
            }
        }
        .resource-pools {
            padding: 0 20px 20px;
        }
        .resource-pool {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-top: 20px;
            overflow: hidden;
        }
        .resource-pool-header {
            padding: 12px 15px;
            font-size: 1.1em;
            font-weight: 600;
            color: #fff;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
         
        .resource-header-total {
            background-color: #3b82f6;  
        }
        .resource-header-intel {
            background-color: #6366f1;  
        }
        .resource-header-arm {
            background-color: #10b981;  
        }
        .resource-header-hg {
            background-color: #8b5cf6;  
        }
        .resource-header-taint {
            background-color: #a855f7;  
        }
        .resource-header-common {
            background-color: #0ea5e9;  
        }
        .resource-header-gpu {
            background-color: #f59e0b;  
        }
        .resource-header-aplus {
            background-color: #ec4899;  
        }
        .resource-header-dplus {
            background-color: #f43f5e;  
        }

        .resource-pool-type {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .node-count-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 0 8px;
            font-size: 0.8em;
            color: white;
            height: 24px;
            margin-left: 8px;
        }
        .node-detail {
            margin-left: 4px;
            font-size: 0.9em;
            opacity: 0.9;
        }
        .resource-pool-info {
            font-size: 0.8em;
            color: rgba(255, 255, 255, 0.9);
            font-weight: normal;
        }
        .resource-usage-stats {
            margin-top: 8px;
            display: flex;
            gap: 12px;
        }
        .usage-stat {
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            padding: 4px 10px;
            display: flex;
            align-items: center;
            font-size: 0.85em;
        }
        .usage-stat-icon {
            margin-right: 6px;
            width: 16px;
            height: 16px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        .usage-stat-label {
            font-weight: 600;
            margin-right: 4px;
        }
        .usage-stat-value {
            opacity: 0.9;
        }
        .usage-warning {
            background-color: rgba(245, 158, 11, 0.3);
        }
        .usage-critical {
            background-color: rgba(239, 68, 68, 0.3);
        }
        .usage-emergency {
            background-color: rgba(220, 38, 38, 0.3);
        }
        .resource-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 10px;
            padding: 15px;
            background-color: white;
        }
         
        .resource-grid .health-card {
            background-color: #f8fafc;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            margin: 0;
        }
        .resource-grid .health-score {
            font-size: 2em;
            margin-bottom: 4px;
        }

         
        .tooltip {
            position: relative;
            display: inline-block;
            cursor: help;
        }
         
        .resource-grid .tooltip .tooltip-text {
            top: 105%;  
            bottom: auto;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.75em;  
            padding: 6px 10px;  
        }
         
        .summary-item.tooltip .tooltip-text {
            top: 105%;
            bottom: auto;
            left: 50%;
            transform: translateX(-50%);
            text-align: left;
            width: 250px;
            font-size: 0.75em;
            padding: 6px 10px;
        }
        .resource-pool-type .tooltip .tooltip-text {
            bottom: auto;
            top: 110%;  
            left: 0;
            transform: none;
            text-align: left;
            font-size: 0.75em;
            padding: 6px 10px;
        }
        .node-type-indicator .tooltip .tooltip-text {
            top: auto;
            bottom: 110%;  
            left: 0;
            transform: none;
            text-align: left;
            width: 250px;
            font-size: 0.75em;
            padding: 6px 10px;
        }
        .node-type-indicator .tooltip .tooltip-text::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 15px;
            border-width: 5px;
            border-style: solid;
            border-color: rgba(0, 0, 0, 0.8) transparent transparent transparent;
        }
        .tooltip .tooltip-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 16px;
            height: 16px;
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            font-size: 10px;
            margin-left: 6px;
            color: white;
        }
        .tooltip .tooltip-text {
            visibility: hidden;
            position: absolute;
            z-index: 1000;  
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 8px 12px;
            width: 220px;
            font-size: 0.8em;
            font-weight: normal;
            line-height: 1.4;
            opacity: 0;
            transition: opacity 0.2s, visibility 0.2s;
            pointer-events: none;  
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);  
        }
        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }
        .node-type-indicator {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 15px;
            padding: 0 15px 15px;
        }
        .node-type-badge {
            background-color: #f1f5f9;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            color: #475569;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        .badge-label {
            font-weight: 600;
            color: #334155;
        }
         
        .resource-overview-table {
            margin: 20px auto 30px;
            max-width: 1000px;
            overflow-x: auto;
            padding: 0 5px;
        }
        .resource-overview-table h2 {
            margin-bottom: 15px;
            color: #1e293b;
            font-size: 1.3em;
            font-weight: 600;
            text-align: center;
            padding-bottom: 8px;
            position: relative;
        }
        .resource-overview-table h2:after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(to right, #3b82f6, #93c5fd);
            border-radius: 3px;
        }
        .overview-table {
            width: 100%;
            table-layout: fixed;
            border-collapse: separate;
            border-spacing: 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 30px;
            background-color: #fff;
            font-size: 0.95em;  
        }
        .overview-table tr {
            height: 38px !important;  
            min-height: 38px !important;
            max-height: 38px !important;
            line-height: normal;
        }
        .overview-table th, .overview-table td {
            padding: 0 6px !important;  
            border-bottom: 1px solid #e2e8f0;
            text-align: center;
            transition: background-color 0.2s;
            overflow: hidden;
            vertical-align: middle;  
            height: 38px !important;  
            min-height: 38px !important;
            max-height: 38px !important;
            line-height: normal;
            box-sizing: border-box;
        }
        .overview-table th {
            background-color: #f1f5f9;
            font-weight: 600;
            color: #1e293b;
            padding: 10px 6px;  
            border-bottom: 2px solid #cbd5e1;
            font-size: 0.85em;
            letter-spacing: 0.5px;
            text-transform: uppercase;
        }
        .overview-table tr:last-child td {
            border-bottom: none;
        }
        .overview-table tr:nth-child(even) {
            background-color: #f8fafc;
        }
        .overview-table tr:hover {
            background-color: #f1f5f9;
        }
        .overview-table td:first-child {
            text-align: left;
            font-weight: 600;
            background-color: #f8fafc;
            color: #334155;
            border-right: 1px solid #e2e8f0;
            width: 20%;  
            padding-left: 10px;
            font-size: 0.9em;  
        }
        .overview-table th:not(:first-child),
        .overview-table td:not(:first-child) {
            width: 20%;
        }
        .resource-indicators {
            display: flex;
            flex-direction: row;  
            gap: 0;  
            position: relative;
            max-width: 180px;
            margin: 0 auto;
            justify-content: center;
        }
        .resource-indicator {
            flex: 1;
            padding: 2px 4px !important;
            height: 20px !important;
            min-height: 20px !important;
            max-height: 20px !important;
            line-height: 16px !important;
            position: relative;
            color: rgba(0, 0, 0, 0.8) !important;
            font-weight: 600;
            text-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
            border: none !important;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 0.7em !important;
        }
         
        .resource-indicator:first-child {
            border-radius: 4px 0 0 4px;
        }
         
        .resource-indicator:last-child {
            border-radius: 0 4px 4px 0;
        }
        .resource-indicator span {
            margin: 0 1px;
        }
        .resource-indicator:hover {
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.12);
            transform: translateY(-1px);
            z-index: 5;
        }
        .resource-indicators:hover {
            z-index: 10;
        }
         
        .resource-indicator.normal {
            background-color: rgba(74, 222, 128, 0.4) !important;  
            border: none !important;
        }
        .resource-indicator.warning {
            background-color: rgba(250, 204, 21, 0.4) !important;  
            border: none !important;
        }
        .resource-indicator.critical {
            background-color: rgba(248, 113, 113, 0.4) !important;  
            border: none !important;
        }
        .resource-indicator.emergency {
            background-color: rgba(239, 68, 68, 0.4) !important;  
            border: none !important;
        }
        .resource-indicator.underutilized {
            background-color: rgba(147, 197, 253, 0.4) !important;  
            border: none !important;
        }
         
         
        .resource-indicator:has(span:contains("85.0%")), 
        .resource-indicator:has(span:contains("90.0%")) {
            background-color: rgba(239, 68, 68, 0.7);  
        }

         
        .resource-indicator:has(span:contains("70.0%")),
        .resource-indicator:has(span:contains("75.0%")) {
            background-color: rgba(250, 204, 21, 0.7);  
        }

         
        .resource-indicator:has(span:contains("50.0%")) {
            background-color: rgba(147, 197, 253, 0.7);  
        }

         
        .resource-indicator:has(span:contains("25.0%")) {
            background-color: rgba(147, 197, 253, 0.6);  
        }

         
        .resource-indicator {
            color: #1a202c;
            font-weight: 600;
            text-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
        }
         
        .resource-card-popup {
            display: none;
            position: absolute;
            z-index: 1000;
            width: 220px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 12px;
            top: 30px;
            left: 50%;
            transform: translateX(-50%);
            text-align: left;
            color: #334155;
            border: 1px solid #e2e8f0;
            animation: fadeIn 0.2s ease-out;
            font-size: 0.85em;
        }

        .resource-card-popup h4 {
            margin: 0 0 8px 0;
            color: #1e293b;
            font-size: 1em;
            border-bottom: 1px solid #e2e8f0;
            padding-bottom: 6px;
        }

        .resource-card-popup p {
            margin: 4px 0;
            line-height: 1.4;
            font-size: 0.9em;
        }

        .resource-card-popup strong {
            color: #475569;
            font-weight: 600;
            margin-right: 4px;
        }

         
        .resource-indicators {
            position: relative;
        }

         
        .resource-indicators:hover .resource-card-popup {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translate(-50%, -5px); }
            to { opacity: 1; transform: translate(-50%, 0); }
        }
        .no-data {
            color: #94a3b8;
            font-style: italic;
            font-size: 0.75em !important;
            padding: 4px 8px !important;
            text-align: center;
            background-color: #f8fafc;
            border-radius: 4px;
            border: 1px dashed #cbd5e1;
            margin: 0 auto;
            max-width: 80%;
            height: 20px !important;  
            min-height: 20px !important;
            max-height: 20px !important;
            display: flex;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
        }
         
        .threshold-indicators {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #e2e8f0;
        }
        .threshold-title {
            display: block;
            font-weight: 600;
            margin-bottom: 10px;
            color: #334155;
            font-size: 0.95em;
        }
        .threshold-group {
            margin-bottom: 10px;
            background-color: #f8fafc;
            border-radius: 6px;
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            display: inline-block;
            margin-right: 10px;
            vertical-align: top;
            width: 100%;  
            box-sizing: border-box;
        }
        .threshold-group:last-child {
            margin-right: 0;
        }
        .threshold-group-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #4a5568;
            font-size: 0.9em;
        }
        .threshold-legend {
            display: flex;
            flex-wrap: nowrap;  
            gap: 4px;  
            margin: 6px 0;
            overflow-x: visible;  
            white-space: nowrap;  
            width: 100%;
        }
        .threshold-item {
            display: flex;
            align-items: center;
            gap: 3px;  
            background-color: white;
            padding: 3px 6px;  
            border-radius: 4px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            font-size: 0.75em;  
            margin-bottom: 4px;
            white-space: nowrap;  
            flex-shrink: 0;  
        }
        .threshold-color {
            width: 10px;
            height: 10px;
            border-radius: 2px;
            flex-shrink: 0;
        }
        .threshold-color.underutilized {
            background-color: #93c5fd;  
        }
        .threshold-color.normal {
            background-color: #4ade80;  
        }
        .threshold-color.warning {
            background-color: #facc15;  
        }
        .threshold-color.critical {
            background-color: #f87171;  
        }
        .threshold-color.emergency {
            background-color: #ef4444;  
        }
        .footer {
            padding: 15px 30px;
            background-color: #f8fafc;
            border-top: 1px solid #e2e8f0;
            font-size: 0.85em;
            color: #64748b;
            text-align: center;
        }
        .note-box {
            background-color: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 8px;
            padding: 12px 15px;
            margin: 15px 0;
            color: #0369a1;
            font-size: 0.9em;
            line-height: 1.4;
        }
        .note-box-title {
            font-weight: 600;
            display: block;
            margin-bottom: 4px;
            font-size: 0.95em;
        }
        .warning-alert {
            background-color: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 15px 20px;
            margin: 20px 0;
            color: #991b1b;
            font-size: 0.95em;
            line-height: 1.5;
        }
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 0;
                max-width: 100%;
            }
            .content {
                padding: 15px;
            }
            .header {
                padding: 15px 20px;
            }
            .header-title {
                font-size: 1.3em;
            }
            .cluster-header {
                font-size: 1.1em;
                padding: 12px 15px;
                flex-direction: column;
                align-items: flex-start;
            }
            .cluster-header-info {
                margin-top: 5px;
            }
             
            .overview-table {
                font-size: 0.8em;
                min-width: 100%;
                border-radius: 8px;
                overflow-x: auto;
                display: block;
            }
            .overview-table th, .overview-table td {
                padding: 4px !important;
                height: auto !important;
                min-height: auto !important;
                max-height: none !important;
            }
            .overview-table td:first-child {
                max-width: 120px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
             
            .resource-indicators {
                max-width: 120px;
            }
            .resource-indicator {
                padding: 1px 2px !important;
                font-size: 0.65em !important;
                height: 16px !important;
                min-height: 16px !important;
                max-height: 16px !important;
                line-height: 14px !important;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        @media (max-width: 480px) {
             
            .cluster-summary {
                grid-template-columns: 1fr;
            }
            .resource-grid {
                grid-template-columns: 1fr;
            }
             
            .overview-table {
                font-size: 0.7em;
            }
            .threshold-item {
                font-size: 0.7em;
                padding: 2px 4px;
            }
             
            .threshold-legend {
                flex-wrap: wrap;
                justify-content: center;
            }
             
            .cluster-header {
                font-size: 1em;
                padding: 10px;
            }
            .cluster-header-info {
                font-size: 0.75em;
            }
             
            .resource-indicators {
                max-width: 100px;
            }
            .resource-indicator {
                font-size: 0.6em !important;
                height: 14px !important;
                min-height: 14px !important;
                max-height: 14px !important;
                line-height: 12px !important;
            }
             
            .tooltip .tooltip-text {
                max-width: 80vw;
                left: 10px;
                right: 10px;
                transform: none;
                font-size: 0.65em;
                width: auto;
            }
        }

         
        @media (max-width: 360px) {
            .container {
                margin: 5px;
            }
            .content {
                padding: 10px;
            }
            .header-title {
                font-size: 1.1em;
            }
            .overview-table {
                font-size: 0.65em;
            }
             
            .overview-table th:nth-child(n+4), 
            .overview-table td:nth-child(n+4) {
                display: none;  
            }
            .resource-indicators {
                max-width: 90px;
            }
        }
         
        .table-controls {
            display: none;  
        }
        .search-box {
            display: none;
        }
        .table-pagination {
            display: none;
        }
        .pagination-info {
            font-size: 0.85em;
            color: #64748b;
        }
        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .pagination-controls button {
            background-color: #f1f5f9;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 0.85em;
            color: #334155;
            cursor: pointer;
            transition: all 0.2s;
        }
        .pagination-controls button:hover:not(:disabled) {
            background-color: #e2e8f0;
        }
        .pagination-controls button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        @media (max-width: 768px) {
            .table-controls {
                flex-direction: row;
                padding: 8px;
            }
            .search-box {
                width: 100%;
            }
        }

         
        .hidden {
            display: none;
        }

         
         
        .resource-indicator {
            color: #1a202c !important;  
            font-weight: 600;
            text-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
            background-color: rgba(255, 255, 255, 0.2);  
            border: none !important;  
            border-radius: 3px;  
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);  
        }

        .resource-indicator.normal {
            background-color: rgba(74, 222, 128, 0.75) !important;  
            border: none !important;
        }
        .resource-indicator.warning {
            background-color: rgba(250, 204, 21, 0.75) !important;  
            border: none !important;
        }
        .resource-indicator.critical {
            background-color: rgba(248, 113, 113, 0.75) !important;  
            border: none !important;
        }
        .resource-indicator.emergency {
            background-color: rgba(239, 68, 68, 0.75) !important;  
            border: none !important;
        }
        .resource-indicator.underutilized {
            background-color: rgba(147, 197, 253, 0.75) !important;  
            border: none !important;
        }

         
        .cpu-85, .mem-90 {
            background-color: rgba(239, 68, 68, 0.35) !important;  
            border: none !important;
        }

        .cpu-70, .mem-75 {
            background-color: rgba(250, 204, 21, 0.35) !important;  
            border: none !important;
        }

        .cpu-50, .mem-50 {
            background-color: rgba(147, 197, 253, 0.35) !important;  
            border: none !important;
        }

        .cpu-25, .mem-25 {
            background-color: rgba(147, 197, 253, 0.3) !important;  
            border: none !important;
        }

         
        .back-to-top {
            position: fixed;
            right: 25px;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            line-height: 40px;
            background: #4a5568;
            color: white;
            text-align: center;
            border-radius: 50%;
            text-decoration: none;
            font-size: 20px;
            opacity: 0.7;
            z-index: 1000;
            transition: opacity 0.3s, transform 0.3s;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        .back-to-top:hover {
            opacity: 1;
            transform: translateY(-50%) scale(1.1);
        }
        
         
        .abnormal-cluster-link {
            color: #ef4444;
            text-decoration: none;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
        }
        
        .abnormal-cluster-link:hover {
            text-decoration: underline;
        }
        
        .abnormal-cluster-icon {
            display: inline-block;
            margin-right: 4px;
            font-size: 0.9em;
        }
        
         
        .cluster-anchor {
            display: block;
            position: relative;
            top: -70px;
            visibility: hidden;
        }
         
        .resource-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 10px;
            padding: 15px;
            background-color: white;
        }
        
         
        .summary-value.warning {
            color: #854d0e;  
            font-weight: 700;
        }

         
        .width-0 { width: 0%; }
        .width-5 { width: 5%; }
        .width-10 { width: 10%; }
        .width-15 { width: 15%; }
        .width-20 { width: 20%; }
        .width-25 { width: 25%; }
        .width-30 { width: 30%; }
        .width-35 { width: 35%; }
        .width-40 { width: 40%; }
        .width-45 { width: 45%; }
        .width-50 { width: 50%; }
        .width-55 { width: 55%; }
        .width-60 { width: 60%; }
        .width-65 { width: 65%; }
        .width-70 { width: 70%; }
        .width-75 { width: 75%; }
        .width-80 { width: 80%; }
        .width-85 { width: 85%; }
        .width-90 { width: 90%; }
        .width-95 { width: 95%; }
        .width-100 { width: 100%; }

         
        .group-header {
            background-color: #f1f5f9;
        }
        
        .group-name {
            font-weight: bold;
            padding: 8px 12px;
            text-align: left;
            background-color: #e2e8f0;
            color: #334155;
            font-size: 16px;
            border-radius: 4px 4px 0 0;
            border-bottom: 2px solid #cbd5e1;
        }
        
         
        .resource-indicators {
            display: flex;
            flex-direction: row;  
            gap: 0;  
            position: relative;
            max-width: 180px;
            margin: 0 auto;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="container">
        
        <div class="header">
            <h1 class="header-title">Kubernetes 集群资源报告</h1>
            <p class="header-date">报告日期: 2025-05-12</p>
        </div>
        <div class="content">
            

                
                <div class="health-summary">
                    <div class="health-card">
                        <span class="health-score">4</span>
                        <span class="health-label">总已巡检集群数</span>
                    </div>
                    <div class="health-card">
                        <span class="health-score health-good">2</span>
                        <span class="health-label">正常集群数</span>
                    </div>
                    <div class="health-card">
                        <span class="health-score health-critical">2</span>
                        <span class="health-label">异常集群数</span>
                    </div>
                    <div class="health-card">
                        <span class="health-score">10.50</span>
                        <span class="health-label">通用集群Pod密度</span>
                    </div>
                </div>

                <div class="note-box">
                    <span class="note-box-title">📊 报告说明</span>
                    
                    <p style="margin: 0 0 8px 0; font-size: 0.9em;">此报告显示<strong>测试环境</strong>资源分配率异常的集群与资源池，以便重点关注潜在风险区域。测试环境低利用率不计为异常，高利用率阈值比生产环境高5%。完整数据已附加在邮件中的Excel文件。</p>
                    
                    <p style="margin: 0; font-size: 0.9em;">资源数据仅计算物理机节点，虚拟机节点仅计算节点数量。</p>

                    <div class="threshold-indicators">
                        <span class="threshold-title">资源分配率说明：</span>

                        
                        
                        <div class="threshold-group">
                            <div class="threshold-group-title">物理机节点数 ≤ 150台（测试环境）：</div>
                            <div class="threshold-legend">
                                <div class="threshold-item"><div class="threshold-color normal"></div> <span>正常 (&lt; 80%)</span></div>
                                <div class="threshold-item"><div class="threshold-color warning"></div> <span>警告 (80% - 90%)</span></div>
                                <div class="threshold-item"><div class="threshold-color critical"></div> <span>危险 (90% - 95%)</span></div>
                                <div class="threshold-item"><div class="threshold-color emergency"></div> <span>紧急 (> 95%)</span></div>
                            </div>
                        </div>

                        <div class="threshold-group">
                            <div class="threshold-group-title">物理机节点数 > 150台（测试环境）：</div>
                            <div class="threshold-legend">
                                <div class="threshold-item"><div class="threshold-color normal"></div> <span>正常 (&lt; 85%)</span></div>
                                <div class="threshold-item"><div class="threshold-color warning"></div> <span>警告 (85% - 90%)</span></div>
                                <div class="threshold-item"><div class="threshold-color critical"></div> <span>危险 (90% - 95%)</span></div>
                                <div class="threshold-item"><div class="threshold-color emergency"></div> <span>紧急 (> 95%)</span></div>
                            </div>
                        </div>
                        
                    </div>
                </div>

                
                <div class="resource-overview-table">
                    <a id="top"></a>
                    <table class="overview-table" id="clusterTable">
                        <thead>
                            <tr>
                                <th>集群名称</th>
                                <th>
                                    通用资源<br>
                                    <span style="font-size: 0.8em; font-weight: normal; color: #64748b; text-transform: lowercase;">total_common</span>
                                </th>
                                <th>
                                    Intel通用<br>
                                    <span style="font-size: 0.8em; font-weight: normal; color: #64748b; text-transform: lowercase;">intel_common</span>
                                </th>
                                <th>
                                    海光通用<br>
                                    <span style="font-size: 0.8em; font-weight: normal; color: #64748b; text-transform: lowercase;">hg_common</span>
                                </th>
                                <th>
                                    ARM通用<br>
                                    <span style="font-size: 0.8em; font-weight: normal; color: #64748b; text-transform: lowercase;">arm_common</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            
                            
                            
                            
                            
                                
                                <tr class="group-header">
                                    <td colspan="5" class="group-name">生产环境</td>
                                </tr>
                            
                            
                            <tr class="cluster-row">
                                <td>
                                    
                                        <a href="#cluster-production-cluster" class="abnormal-cluster-link">
                                            <span class="abnormal-cluster-icon">⚠️</span>production-cluster
                                        </a>
                                    
                                </td>
                                
                                 
                                    <td>
                                        
                                            <div class="no-data">无资源</div>
                                        
                                    </td>
                                 
                                    <td>
                                        
                                            <div class="resource-indicators">
                                                <div class="resource-indicator critical">90.0%</div>
                                                <div class="resource-indicator emergency">95.0%</div>
                                                <div class="resource-card-popup">
                                                    <h4>Intel通用 (intel_common)</h4>
                                                    <p><strong>CPU分配率:</strong> 90.0% (216.0/240.0 核)</p>
                                                    <p><strong>内存分配率:</strong> 95.0% (456.0/480.0 GiB)</p>
                                                    <p><strong>节点数:</strong> 15 个 (10 物理机)</p>
                                                    
                                                </div>
                                            </div>
                                        
                                    </td>
                                 
                                    <td>
                                        
                                            <div class="no-data">无资源</div>
                                        
                                    </td>
                                 
                                    <td>
                                        
                                            <div class="no-data">无资源</div>
                                        
                                    </td>
                                
                            </tr>
                            
                            
                            
                            
                                
                                <tr class="group-header">
                                    <td colspan="5" class="group-name">测试环境</td>
                                </tr>
                            
                            
                            <tr class="cluster-row">
                                <td>
                                    
                                        <a href="#cluster-test-cluster" class="abnormal-cluster-link">
                                            <span class="abnormal-cluster-icon">⚠️</span>test-cluster
                                        </a>
                                    
                                </td>
                                
                                 
                                    <td>
                                        
                                            <div class="no-data">无资源</div>
                                        
                                    </td>
                                 
                                    <td>
                                        
                                            <div class="no-data">无资源</div>
                                        
                                    </td>
                                 
                                    <td>
                                        
                                            <div class="no-data">无资源</div>
                                        
                                    </td>
                                 
                                    <td>
                                        
                                            <div class="no-data">无资源</div>
                                        
                                    </td>
                                
                            </tr>
                            
                            
                            
                            
                                
                                <tr class="group-header">
                                    <td colspan="5" class="group-name">低利用率集群</td>
                                </tr>
                            
                            
                            <tr class="cluster-row">
                                <td>
                                    
                                        <a href="#cluster-underutilized-cluster" class="abnormal-cluster-link">
                                            <span class="abnormal-cluster-icon">⚠️</span>underutilized-cluster
                                        </a>
                                    
                                </td>
                                
                                 
                                    <td>
                                        
                                            <div class="no-data">无资源</div>
                                        
                                    </td>
                                 
                                    <td>
                                        
                                            <div class="resource-indicators">
                                                <div class="resource-indicator normal">50.0%</div>
                                                <div class="resource-indicator normal">50.0%</div>
                                                <div class="resource-card-popup">
                                                    <h4>Intel通用 (intel_common)</h4>
                                                    <p><strong>CPU分配率:</strong> 50.0% (80.0/160.0 核)</p>
                                                    <p><strong>内存分配率:</strong> 50.0% (160.0/320.0 GiB)</p>
                                                    <p><strong>节点数:</strong> 10 个 (6 物理机)</p>
                                                    
                                                </div>
                                            </div>
                                        
                                    </td>
                                 
                                    <td>
                                        
                                            <div class="resource-indicators">
                                                <div class="resource-indicator normal">50.0%</div>
                                                <div class="resource-indicator normal">50.0%</div>
                                                <div class="resource-card-popup">
                                                    <h4>海光通用 (hg_common)</h4>
                                                    <p><strong>CPU分配率:</strong> 50.0% (40.0/80.0 核)</p>
                                                    <p><strong>内存分配率:</strong> 50.0% (80.0/160.0 GiB)</p>
                                                    <p><strong>节点数:</strong> 5 个 (3 物理机)</p>
                                                    
                                                </div>
                                            </div>
                                        
                                    </td>
                                 
                                    <td>
                                        
                                            <div class="resource-indicators">
                                                <div class="resource-indicator normal">50.0%</div>
                                                <div class="resource-indicator normal">50.0%</div>
                                                <div class="resource-card-popup">
                                                    <h4>ARM通用 (arm_common)</h4>
                                                    <p><strong>CPU分配率:</strong> 50.0% (40.0/80.0 核)</p>
                                                    <p><strong>内存分配率:</strong> 50.0% (80.0/160.0 GiB)</p>
                                                    <p><strong>节点数:</strong> 5 个 (3 物理机)</p>
                                                    
                                                </div>
                                            </div>
                                        
                                    </td>
                                
                            </tr>
                            
                            
                            
                            
                                
                                <tr class="group-header">
                                    <td colspan="5" class="group-name">极低利用率集群</td>
                                </tr>
                            
                            
                            <tr class="cluster-row">
                                <td>
                                    
                                        <a href="#cluster-very-underutilized-cluster" class="abnormal-cluster-link">
                                            <span class="abnormal-cluster-icon">⚠️</span>very-underutilized-cluster
                                        </a>
                                    
                                </td>
                                
                                 
                                    <td>
                                        
                                            <div class="no-data">无资源</div>
                                        
                                    </td>
                                 
                                    <td>
                                        
                                            <div class="no-data">无资源</div>
                                        
                                    </td>
                                 
                                    <td>
                                        
                                            <div class="no-data">无资源</div>
                                        
                                    </td>
                                 
                                    <td>
                                        
                                            <div class="no-data">无资源</div>
                                        
                                    </td>
                                
                            </tr>
                            
                        </tbody>
                    </table>
                </div>

                
                    
                    
                    <div class="cluster-section">
                        <a id="cluster-production-cluster" class="cluster-anchor"></a>
                        <div class="cluster-header">
                            <span>集群: production-cluster （生产环境）</span>
                        </div>

                        <div class="resource-pools">
                            
                                
                                <div class="resource-pool">
                                    <div class="resource-pool-header resource-header-total">
                                        <div class="resource-pool-type">
                                            <span class="tooltip">
                                                总资源 (total)
                                                <span class="tooltip-icon">?</span>
                                                <span class="tooltip-text">
                                                    
                                                        集群所有物理机资源总和，包含集群中所有类型的节点。
                                                    
                                                </span>
                                            </span>
                                            <span class="node-count-badge">30 节点 <span class="node-detail">(物理机: 20, 虚拟机: 10)</span></span>
                                        </div>
                                    </div>
                                    <div class="resource-grid">
                                        <div class="health-card tooltip">
                                            <span class="health-score">
                                                
                                                    15.0
                                                
                                            </span>
                                            <span class="health-label">Pod密度</span>
                                            <span class="tooltip-text">
                                                Pod密度表示每台物理机上运行的Pod数量。计算方式：Pod总数 / 物理机数量。该指标可以反映集群的资源利用效率。
                                            </span>
                                        </div>
                                        
                                            <div class="summary-item tooltip warning">
                                                <span class="summary-label">CPU 分配率 (408.0/480.0 核)</span>
                                                <span class="summary-value warning">
                                                    85.0%
                                                </span>
                                                <span class="tooltip-text">
                                                    当前值：408.0/480.0 核 (85.0%)<br><br>状态：<b>警告</b> - 资源池CPU分配率在80%-90%之间，建议关注资源趋势。
                                                </span>
                                                <div class="usage-bar-container">
                                                    <div class="usage-bar high-usage width-85"></div>
                                                </div>

                                                
                                                
                                                <table class="trend-container" cellspacing="0" cellpadding="0">
                                                    <tr>
                                                        
                                                            <td class="trend-day bar-normal" title="6天前 CPU分配率: 78.0%"></td>
                                                        
                                                            <td class="trend-day bar-warning" title="5天前 CPU分配率: 80.0%"></td>
                                                        
                                                            <td class="trend-day bar-warning" title="4天前 CPU分配率: 82.0%"></td>
                                                        
                                                            <td class="trend-day bar-warning" title="3天前 CPU分配率: 85.0%"></td>
                                                        
                                                            <td class="trend-day bar-warning" title="2天前 CPU分配率: 83.0%"></td>
                                                        
                                                            <td class="trend-day bar-warning" title="昨天 CPU分配率: 84.0%"></td>
                                                        
                                                            <td class="trend-day bar-warning" title="今天 CPU分配率: 85.0%"></td>
                                                        
                                                    </tr>
                                                </table>
                                                <span class="trend-label">7天波动</span>
                                                
                                            </div>
                                        

                                        
                                            
                                            
                                                
                                            
                                            
                                            
                                            
                                            
                                            <div class="summary-item tooltip critical">
                                                <span class="summary-label">内存分配率 (864.0/960.0 GiB)</span>
                                                <span class="summary-value critical">
                                                    90.0%
                                                </span>
                                                <span class="tooltip-text">
                                                    当前值：864.0/960.0 GiB (90.0%)<br/>状态：<b>危险</b> - 资源池内存分配率在90%-95%之间，需要尽快关注。
                                                </span>
                                                
                                                
                                                
                                                
                                                
                                                <div class="usage-bar-container">
                                                    <div class="usage-bar danger-usage width-90"></div>
                                                </div>

                                                
                                                
                                                <table class="trend-container" cellspacing="0" cellpadding="0">
                                                    <tr>
                                                        
                                                        
                                                            
                                                            
                                                            
                                                            
                                                                
                                                                
                                                            
                                                            
                                                            
                                                            
                                                            <td class="trend-day bar-warning" 
                                                                title="6天前 内存分配率: 82.0%"></td>
                                                        
                                                            
                                                            
                                                            
                                                            
                                                                
                                                                
                                                            
                                                            
                                                            
                                                            
                                                            <td class="trend-day bar-warning" 
                                                                title="5天前 内存分配率: 85.0%"></td>
                                                        
                                                            
                                                            
                                                            
                                                            
                                                                
                                                                
                                                            
                                                            
                                                            
                                                            
                                                            <td class="trend-day bar-warning" 
                                                                title="4天前 内存分配率: 87.0%"></td>
                                                        
                                                            
                                                            
                                                            
                                                            
                                                                
                                                                
                                                            
                                                            
                                                            
                                                            
                                                            <td class="trend-day bar-critical" 
                                                                title="3天前 内存分配率: 90.0%"></td>
                                                        
                                                            
                                                            
                                                            
                                                            
                                                                
                                                                
                                                            
                                                            
                                                            
                                                            
                                                            <td class="trend-day bar-warning" 
                                                                title="2天前 内存分配率: 88.0%"></td>
                                                        
                                                            
                                                            
                                                            
                                                            
                                                                
                                                            
                                                            
                                                            
                                                            
                                                            <td class="trend-day bar-warning" 
                                                                title="昨天 内存分配率: 89.0%"></td>
                                                        
                                                            
                                                            
                                                            
                                                            
                                                                
                                                            
                                                            
                                                            
                                                            
                                                            <td class="trend-day bar-critical" 
                                                                title="今天 内存分配率: 90.0%"></td>
                                                        
                                                    </tr>
                                                </table>
                                                <span class="trend-label">7天波动</span>
                                                
                                            </div>
                                        
                                    </div>

                                    <div class="node-type-indicator">
                                        <div class="node-type-badge tooltip">
                                            <span>Pod数量:</span>
                                            <span class="badge-label">300</span>
                                            <span class="tooltip-text">按天分布取过去一个月中某天最大的已发布pod数（不区分在线状态）</span>
                                        </div>
                                        <div class="node-type-badge tooltip">
                                            <span>平均CPU:</span>
                                            <span class="badge-label">13.6核</span>
                                            <span class="tooltip-text">已分配的cpu/物理机节点，即每节点平均分配cpu</span>
                                        </div>
                                        <div class="node-type-badge tooltip">
                                            <span>平均内存:</span>
                                            <span class="badge-label">28.8GiB</span>
                                            <span class="tooltip-text">已分配的内存/物理机节点，即每节点平均分配内存</span>
                                        </div>
                                        <div class="node-type-badge tooltip">
                                            <span>CPU最大使用率:</span>
                                            <span class="badge-label">0.0%</span>
                                            <span class="tooltip-text">过去24小时内CPU平均最大使用率</span>
                                        </div>
                                        <div class="node-type-badge tooltip">
                                            <span>内存最大使用率:</span>
                                            <span class="badge-label">0.0%</span>
                                            <span class="tooltip-text">过去24小时内内存平均最大使用率</span>
                                        </div>
                                    </div>
                                </div>
                                
                            
                                
                                <div class="resource-pool">
                                    <div class="resource-pool-header resource-header-intel">
                                        <div class="resource-pool-type">
                                            <span class="tooltip">
                                                Intel通用节点 (intel_common)
                                                <span class="tooltip-icon">?</span>
                                                <span class="tooltip-text">
                                                    
                                                        集群所有物理机资源总和，包含集群中所有类型的节点。
                                                    
                                                </span>
                                            </span>
                                            <span class="node-count-badge">15 节点 <span class="node-detail">(物理机: 10, 虚拟机: 5)</span></span>
                                        </div>
                                    </div>
                                    <div class="resource-grid">
                                        <div class="health-card tooltip">
                                            <span class="health-score">
                                                
                                                    16.0
                                                
                                            </span>
                                            <span class="health-label">Pod密度</span>
                                            <span class="tooltip-text">
                                                Pod密度表示每台物理机上运行的Pod数量。计算方式：Pod总数 / 物理机数量。该指标可以反映集群的资源利用效率。
                                            </span>
                                        </div>
                                        
                                            <div class="summary-item tooltip critical">
                                                <span class="summary-label">CPU 分配率 (216.0/240.0 核)</span>
                                                <span class="summary-value critical">
                                                    90.0%
                                                </span>
                                                <span class="tooltip-text">
                                                    当前值：216.0/240.0 核 (90.0%)<br><br>状态：<b>危险</b> - 资源池CPU分配率在90%-95%之间，需要尽快关注。
                                                </span>
                                                <div class="usage-bar-container">
                                                    <div class="usage-bar danger-usage width-90"></div>
                                                </div>

                                                
                                                
                                                <table class="trend-container" cellspacing="0" cellpadding="0">
                                                    <tr>
                                                        
                                                            <td class="trend-day bar-warning" title="6天前 CPU分配率: 85.0%"></td>
                                                        
                                                            <td class="trend-day bar-warning" title="5天前 CPU分配率: 87.0%"></td>
                                                        
                                                            <td class="trend-day bar-warning" title="4天前 CPU分配率: 88.0%"></td>
                                                        
                                                            <td class="trend-day bar-critical" title="3天前 CPU分配率: 90.0%"></td>
                                                        
                                                            <td class="trend-day bar-critical" title="2天前 CPU分配率: 92.0%"></td>
                                                        
                                                            <td class="trend-day bar-critical" title="昨天 CPU分配率: 91.0%"></td>
                                                        
                                                            <td class="trend-day bar-critical" title="今天 CPU分配率: 90.0%"></td>
                                                        
                                                    </tr>
                                                </table>
                                                <span class="trend-label">7天波动</span>
                                                
                                            </div>
                                        

                                        
                                            
                                            
                                                
                                            
                                            
                                            
                                            
                                            
                                            <div class="summary-item tooltip emergency">
                                                <span class="summary-label">内存分配率 (456.0/480.0 GiB)</span>
                                                <span class="summary-value emergency">
                                                    95.0%
                                                </span>
                                                <span class="tooltip-text">
                                                    当前值：456.0/480.0 GiB (95.0%)<br/>状态：<b>紧急</b> - 资源池内存分配率超过95%，需要立即关注并采取措施。
                                                </span>
                                                
                                                
                                                
                                                
                                                
                                                <div class="usage-bar-container">
                                                    <div class="usage-bar emergency-usage width-95"></div>
                                                </div>

                                                
                                                
                                                <table class="trend-container" cellspacing="0" cellpadding="0">
                                                    <tr>
                                                        
                                                        
                                                            
                                                            
                                                            
                                                            
                                                                
                                                                
                                                            
                                                            
                                                            
                                                            
                                                            <td class="trend-day bar-critical" 
                                                                title="6天前 内存分配率: 90.0%"></td>
                                                        
                                                            
                                                            
                                                            
                                                            
                                                                
                                                                
                                                            
                                                            
                                                            
                                                            
                                                            <td class="trend-day bar-critical" 
                                                                title="5天前 内存分配率: 92.0%"></td>
                                                        
                                                            
                                                            
                                                            
                                                            
                                                                
                                                                
                                                            
                                                            
                                                            
                                                            
                                                            <td class="trend-day bar-critical" 
                                                                title="4天前 内存分配率: 93.0%"></td>
                                                        
                                                            
                                                            
                                                            
                                                            
                                                                
                                                                
                                                            
                                                            
                                                            
                                                            
                                                            <td class="trend-day bar-critical" 
                                                                title="3天前 内存分配率: 94.0%"></td>
                                                        
                                                            
                                                            
                                                            
                                                            
                                                                
                                                                
                                                            
                                                            
                                                            
                                                            
                                                            <td class="trend-day bar-emergency" 
                                                                title="2天前 内存分配率: 96.0%"></td>
                                                        
                                                            
                                                            
                                                            
                                                            
                                                                
                                                            
                                                            
                                                            
                                                            
                                                            <td class="trend-day bar-emergency" 
                                                                title="昨天 内存分配率: 95.0%"></td>
                                                        
                                                            
                                                            
                                                            
                                                            
                                                                
                                                            
                                                            
                                                            
                                                            
                                                            <td class="trend-day bar-emergency" 
                                                                title="今天 内存分配率: 95.0%"></td>
                                                        
                                                    </tr>
                                                </table>
                                                <span class="trend-label">7天波动</span>
                                                
                                            </div>
                                        
                                    </div>

                                    <div class="node-type-indicator">
                                        <div class="node-type-badge tooltip">
                                            <span>Pod数量:</span>
                                            <span class="badge-label">160</span>
                                            <span class="tooltip-text">按天分布取过去一个月中某天最大的已发布pod数（不区分在线状态）</span>
                                        </div>
                                        <div class="node-type-badge tooltip">
                                            <span>平均CPU:</span>
                                            <span class="badge-label">14.4核</span>
                                            <span class="tooltip-text">已分配的cpu/物理机节点，即每节点平均分配cpu</span>
                                        </div>
                                        <div class="node-type-badge tooltip">
                                            <span>平均内存:</span>
                                            <span class="badge-label">30.4GiB</span>
                                            <span class="tooltip-text">已分配的内存/物理机节点，即每节点平均分配内存</span>
                                        </div>
                                        <div class="node-type-badge tooltip">
                                            <span>CPU最大使用率:</span>
                                            <span class="badge-label">0.0%</span>
                                            <span class="tooltip-text">过去24小时内CPU平均最大使用率</span>
                                        </div>
                                        <div class="node-type-badge tooltip">
                                            <span>内存最大使用率:</span>
                                            <span class="badge-label">0.0%</span>
                                            <span class="tooltip-text">过去24小时内内存平均最大使用率</span>
                                        </div>
                                    </div>
                                </div>
                                
                            
                        </div>
                    </div>
                    
                
                    
                    
                    <div class="cluster-section">
                        <a id="cluster-test-cluster" class="cluster-anchor"></a>
                        <div class="cluster-header">
                            <span>集群: test-cluster （测试环境）</span>
                        </div>

                        <div class="resource-pools">
                            
                                
                            
                        </div>
                    </div>
                    
                
                    
                    
                    <div class="cluster-section">
                        <a id="cluster-underutilized-cluster" class="cluster-anchor"></a>
                        <div class="cluster-header">
                            <span>集群: underutilized-cluster （低利用率集群）</span>
                        </div>

                        <div class="resource-pools">
                            
                                
                            
                                
                            
                                
                            
                                
                            
                        </div>
                    </div>
                    
                
                    
                    
                    <div class="cluster-section">
                        <a id="cluster-very-underutilized-cluster" class="cluster-anchor"></a>
                        <div class="cluster-header">
                            <span>集群: very-underutilized-cluster （极低利用率集群）</span>
                        </div>

                        <div class="resource-pools">
                            
                                
                            
                        </div>
                    </div>
                    
                

                
            
        </div>
        <div class="footer">
            此邮件为系统自动生成，完整数据请查看附件Excel文件。如有问题请联系运维团队。
        </div>
    </div>
    <a href="#top" style="display: block; position: fixed; top: 50%; right: 20px; transform: translateY(-50%); width: 36px; height: 36px; background-color: #2196F3; color: white; text-align: center; border-radius: 50%; text-decoration: none; box-shadow: 0 2px 5px rgba(0,0,0,0.2); line-height: 36px; font-size: 20px; opacity: 0.8;">↑</a>
</body>
</html>



