<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: 'Segoe UI', Aria<PERSON>, sans-serif;
            background: #f8fafc;
            color: #334155;
            margin: 0;
            padding: 0;
            line-height: 1.5;
        }
        .container {
            max-width: 840px;
            margin: 20px auto; /* 减小外边距 */
            background: #fff;
            border-radius: 12px; /* 减小圆角 */
            box-shadow: 0 2px 10px rgba(0,0,0,0.05); /* 调整阴影 */
            padding: 0;
            overflow: hidden;
        }
        .header {
            /* background: linear-gradient(135deg, #334155 0%, #1e293b 100%); */ /* 可以考虑简化背景 */
            background-color: #2d3748;
            color: white;
            padding: 20px 24px; /* 调整内边距 */
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap; /* 允许换行 */
            gap: 10px; /* 添加间距 */
        }
        .header-title {
            font-size: 1.4em; /* 调整字体大小 */
            font-weight: 600;
            letter-spacing: 0.5px;
        }
        .header-date {
            font-size: 0.9em;
            opacity: 0.85;
        }
        .health-summary {
            display: grid;
            /* grid-template-columns: 1fr 1fr 1fr 1fr; */ /* 移除固定列数 */
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); /* 自动适应列数 */
            border-bottom: 1px solid #e2e8f0;
            gap: 1px; /* 用间隙代替边框 */
            background-color: #e2e8f0; /* 背景填充间隙 */
        }
        .health-card {
            padding: 15px; /* 调整内边距 */
            text-align: center;
            /* border-right: 1px solid #e2e8f0; */ /* 移除右边框 */
            background-color: #fff; /* 卡片背景 */
        }
        .health-card:last-child {
            /* border-right: none; */ /* 移除 */
        }
        .health-score {
            font-size: 2.5em;
            font-weight: 700;
            display: block;
            margin-bottom: 6px;
        }
        .health-label {
            font-size: 0.95em;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: block;
            font-weight: 500;
        }
        .health-good { color: #10b981; }
        .health-warning { color: #f59e0b; }
        .health-critical { color: #ef4444; }

        .main-content {
            padding: 32px;
        }

        .alert-box {
            border-radius: 10px;
            padding: 20px 24px;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            gap: 20px;
        }
        .alert-icon {
            font-size: 2.2em;
            flex-shrink: 0;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }
        .alert-content {
            flex-grow: 1;
        }
        .alert-title {
            font-size: 1.3em;
            font-weight: 600;
            margin-bottom: 4px;
        }
        .alert-message {
            margin: 0;
            font-size: 0.95em;
        }
        .alert-critical {
            background-color: #fef2f2;
            border-left: 6px solid #ef4444;
        }
        .alert-critical .alert-icon {
            background-color: #fee2e2;
            color: #ef4444;
        }
        .alert-critical .alert-title {
            color: #b91c1c;
        }
        .alert-good {
            background-color: #f0fdf4;
            border-left: 6px solid #10b981;
        }
        .alert-good .alert-icon {
            background-color: #dcfce7;
            color: #10b981;
        }
        .alert-good .alert-title {
            color: #047857;
        }

        .alert-warning {
            background-color: #fffbeb;
            border-left: 6px solid #f59e0b;
        }
        .alert-warning .alert-icon {
            background-color: #fef3c7;
            color: #f59e0b;
        }
        .alert-warning .alert-title {
            color: #b45309;
        }

        .section {
            margin-bottom: 36px;
        }
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            border-bottom: 1px solid #e2e8f0;
            padding-bottom: 12px;
        }
        .section-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .section-icon {
            background: #f1f5f9;
            color: #64748b;
            border-radius: 6px;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9em;
        }

        /* Dashboard grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 24px;
            margin-bottom: 36px;
        }
        .dashboard-card {
            background: #fff;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 2px 6px rgba(0,0,0,0.03);
            overflow: hidden;
        }
        .dashboard-card-header {
            padding: 14px 18px;
            font-weight: 600;
            color: #334155;
            font-size: 1em;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }
        .dashboard-card-body {
            padding: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Charts styling */
        .chart-container {
            width: 100%;
            height: 220px;
        }

        /* Table styling */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 0;
            font-size: 0.95em;
        }
        .cluster-health-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            font-size: 0.9em;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
        }
        .data-table th,
        .data-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        .cluster-health-table th,
        .cluster-health-table td {
            padding: 10px 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
            vertical-align: middle;
        }
        .data-table th {
            background-color: #f8fafc;
            font-weight: 600;
            color: #475569;
        }
        .cluster-health-table th {
            background-color: #f8fafc;
            font-weight: 600;
            color: #475569;
        }
        .data-table tr:last-child td {
            border-bottom: none;
        }
        .cluster-health-table tr:last-child td {
            border-bottom: none;
        }
        .data-table td.highlight {
            font-weight: 500;
        }
        .cluster-health-table td.highlight {
            font-weight: 500;
        }
        .cluster-row:hover {
            background-color: #f8fafc;
            transition: background-color 0.2s;
        }
        .cluster-row[data-is-healthy="true"] td:first-child {
            border-left: 3px solid #10b981;
        }
        .cluster-row[data-is-healthy="false"] td:first-child {
            border-left: 3px solid #ef4444;
        }

        /* Fix for cluster row borders */
        .cluster-row[data-status="red"] td:first-child {
            border-left: 3px solid #ef4444;
        }
        .cluster-row[data-status="green"] td:first-child {
            border-left: 3px solid #10b981;
        }

        /* Status indicators */
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .status-dot {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
        }
        .status-dot.red {
            background-color: #ef4444;
        }
        .status-dot.green {
            background-color: #10b981;
        }
        .status-dot.yellow {
            background-color: #f59e0b;
        }
        .status-text {
            font-weight: 500;
        }

        .view-details-link {
            color: #3b82f6;
            text-decoration: none;
            font-weight: 500;
            display: inline-block;
            padding: 6px 12px;
            background-color: #eff6ff;
            border-radius: 4px;
            transition: all 0.2s;
        }
        .view-details-link:hover {
            text-decoration: none;
            background-color: #dbeafe;
            transform: translateY(-1px);
        }
        .no-details {
            color: #94a3b8;
            font-style: italic;
            display: inline-block;
            padding: 6px 12px;
        }

        /* Critical issue cards */
        .issue-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .issue-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid #f1f5f9;
            overflow: hidden;
        }
        .issue-card-header {
            background: #fef2f2;
            padding: 12px 16px;
            font-weight: 600;
            color: #b91c1c;
            border-bottom: 1px solid #fee2e2;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .issue-location {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .issue-card-body {
            padding: 16px;
        }
        .issue-detail {
            margin-bottom: 12px;
            padding-bottom: 12px;
            border-bottom: 1px dashed #f1f5f9;
        }
        .issue-detail:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        .issue-name {
            font-weight: 500;
            color: #334155;
            margin-bottom: 4px;
        }
        .issue-value {
            color: #ef4444;
            font-family: monospace;
            background: #fef2f2;
            padding: 3px 8px;
            border-radius: 4px;
            margin-bottom: 4px;
            font-size: 0.9em;
        }
        .issue-solution {
            color: #64748b;
            font-size: 0.9em;
            font-style: italic;
        }

        /* Action buttons */
        .action-button {
            display: inline-block;
            padding: 8px 20px;
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            border-radius: 8px;
            font-weight: 500;
            text-decoration: none;
            margin-top: 8px;
            box-shadow: 0 2px 6px rgba(37, 99, 235, 0.3);
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
        }
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
        }

        /* Footer styling */
        .footer {
            background: #f8fafc;
            padding: 20px 32px;
            color: #64748b;
            font-size: 0.9em;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }

        /* 热力图样式 */
        .heat-level-high {
            color: #ef4444;
            font-weight: 600;
        }
        .heat-level-2 {
            color: #f59e0b;
            font-weight: 500;
        }
        .heat-level-1 {
            color: #f97316;
         }

        /* 添加CSS和JavaScript */
        .hidden-node {
            display: block !important;
        }

        /* 新增样式：隐藏正常集群 */
        .hidden-cluster {
            display: none;
        }
        .section-note {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
            font-style: italic;
        }
        .button-container {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 10px;
        }
        .view-all-button {
            display: flex;
            align-items: center;
            background: none;
            border: none;
            color: #4a90e2;
            cursor: pointer;
            font-size: 14px;
        }
        .arrow {
            display: inline-block;
            margin-left: 5px;
            border: solid #4a90e2;
            border-width: 0 2px 2px 0;
            padding: 3px;
            transition: transform 0.3s;
        }
        .arrow.down {
            transform: rotate(45deg);
        }
        .arrow.up {
            transform: rotate(-135deg);
        }

        /* 节点数量链接样式 */
        .count-link {
            display: inline-block;
            color: #3b82f6;
            font-weight: 600;
            text-decoration: none;
            padding: 2px 8px;
            border-radius: 4px;
            background-color: #eff6ff;
            transition: all 0.2s;
        }
        .count-link:hover {
            background-color: #dbeafe;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
        }

        /* 未巡检节点集群样式 */
        .missing-cluster-section {
            margin-bottom: 24px;
        }
        .cluster-title {
            color: #334155;
            font-size: 1.1em;
            margin-bottom: 12px;
            padding-bottom: 6px;
            border-bottom: 1px dashed #e2e8f0;
        }

        /* 折叠相关样式 */
        .cluster-header {
            cursor: pointer;
        }
        .cluster-content {
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        .cluster-content.hidden {
            display: block !important;
        }
        .toggle-icon {
            display: inline-block;
            width: 20px;
            height: 20px;
            margin-right: 5px;
            cursor: pointer;
            position: relative;
            top: 2px;
         }

        /* 移除所有需要JavaScript的样式 */
        .hidden, .hidden-node {
            display: block !important;
        }
        .cluster-content.hidden {
            display: block !important;
        }

        /* 响应式设计 - 手机适配 */
        @media (max-width: 768px) {
            .container {
                margin: 10px; /* 移动端进一步减小外边距 */
                border-radius: 8px;
                width: auto; /* 移除固定宽度 */
                max-width: calc(100% - 20px); /* 考虑边距 */
                box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            }

            /* 页眉调整 */
            .header {
                padding: 15px 20px;
                /* flex-direction: column; */ /* 保持flex布局 */
                /* align-items: flex-start; */
            }
            .header-title {
                font-size: 1.3em;
                /* margin-bottom: 8px; */ /* 由gap处理 */
            }

            /* 健康卡片调整 */
            .health-summary {
                 grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); /* 调整最小宽度 */
            }
            .health-card {
                padding: 12px;
                /* border-bottom: 1px solid #e2e8f0; */ /* 由父级gap处理 */
            }
            /* 移除旧的边框规则 */
            /* .health-card:nth-child(odd) { ... } */
            /* .health-card:nth-child(even) { ... } */
            /* .health-card:nth-child(3), .health-card:nth-child(4) { ... } */
            .health-score {
                font-size: 1.8em;
            }

            /* 主内容区域调整 */
            .main-content {
                padding: 20px 15px;
            }

            /* 警告框调整 */
            .alert-box {
                padding: 15px;
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }
            .alert-icon {
                width: 50px;
                height: 50px;
                margin: 0 auto;
            }
            .alert-title {
                font-size: 1.1em;
            }

            /* 仪表板网格调整 */
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            /* 表格调整 - 水平滚动 */
            .cluster-health-table {
                display: block; /* 使其可滚动 */
                overflow-x: auto;
                width: 100%;
                -webkit-overflow-scrolling: touch;
                font-size: 0.85em;
                border-radius: 0; /* 滚动时移除圆角 */
                border-left: none;
                border-right: none;
                border-top: 1px solid #e2e8f0; /* 保持顶部边框 */
                border-bottom: 1px solid #e2e8f0; /* 保持底部边框 */
            }
             .cluster-health-table thead, .cluster-health-table tbody, .cluster-health-table tr {
                display: block;
            }
            .cluster-health-table th,
            .cluster-health-table td {
                /* padding: 10px 12px; */ /* 调整内边距 */
                padding: 8px 10px;
                white-space: nowrap; /* 防止滚动时换行 */
                display: inline-block; /* 水平排列 */
                width: 130px; /* 固定宽度 */
                vertical-align: top;
            }
             .cluster-health-table td:first-child, .cluster-health-table th:first-child {
                 width: 160px; /* 集群名称列稍宽 */
             }
             .cluster-health-table tr {
                 border-bottom: 1px solid #e2e8f0;
                 display: block; /* 确保 tr 也是 block */
                 white-space: nowrap; /* 防止内部元素换行导致 tr 高度变化 */
             }
             .cluster-health-table tr:last-child {
                 border-bottom: none;
             }
             .cluster-health-table tbody tr:hover {
                 background-color: inherit;
             }
            /* 确保表格边框在移动端显示正确 */
            .cluster-row[data-is-healthy="true"] td:first-child,
            .cluster-row[data-is-healthy="false"] td:first-child,
            .cluster-row[data-status="red"] td:first-child,
            .cluster-row[data-status="green"] td:first-child {
                border-left-width: 3px;
            }

            /* 异常卡片调整 */
            .issue-cards {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            .issue-card {
                margin-bottom: 0;
            }

            /* 未巡检节点部分调整 */
            .missing-cluster-section {
                margin-bottom: 15px;
            }

            /* 数据表格调整 */
            .data-table {
                display: block;
                overflow-x: auto;
                width: 100%;
                -webkit-overflow-scrolling: touch;
                font-size: 0.85em;
            }

            /* 集群标题调整 */
            .cluster-title {
                font-size: 1em;
                word-break: break-word;
            }

            /* 链接样式调整 */
            .count-link {
                padding: 2px 6px;
                font-size: 0.9em;
            }

            /* 其他元素调整 */
            .section-title {
                font-size: 1.1em;
                flex-wrap: wrap;
            }
            .section-icon {
                width: 24px;
                height: 24px;
            }
        }

        }

        @media (max-width: 480px) { /* 增加 480px 断点 */
             .header {
                 padding: 12px 15px;
             }
             .header-title {
                 font-size: 1.2em;
             }
             .health-summary {
                 grid-template-columns: 1fr 1fr; /* 小屏幕固定两列 */
             }
             .health-card {
                 padding: 10px;
             }
             .health-score {
                 font-size: 1.6em;
             }
             .cluster-health-table th,
             .cluster-health-table td {
                 width: 110px; /* 进一步减小宽度 */
                 padding: 6px 8px;
             }
             .cluster-health-table td:first-child, .cluster-health-table th:first-child {
                 width: 140px;
             }
             /* ... existing code ... */
        }

        /* 针对超小屏幕设备的额外优化 */
        @media (max-width: 375px) {
            .health-card {
                padding: 10px;
            }
            .health-score {
                font-size: 1.8em;
            }
            .main-content {
                padding: 15px 10px;
            }
            .issue-card-header {
                flex-direction: column;
                align-items: flex-start;
            }
            .issue-location {
                margin-top: 4px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-title">节点安全基线巡检报告</div>
            <div class="header-date">生成时间: {{now | date "2006-01-02 15:04:05"}}</div>
                </div>

        <!-- Health summary -->
        <!-- Cluster Health summary -->
        <div class="health-summary">
            <div class="health-card">
                <span class="health-score">{{.TotalClusters}}</span>
                <span class="health-label">总集群数</span>
            </div>
            <div class="health-card">
                <span class="health-score health-good">{{.NormalClusters}}</span>
                <span class="health-label">正常集群</span>
                </div>
            <div class="health-card">
                <span class="health-score {{if gt .UnscannedClusters 0}}health-warning{{else}}health-good{{end}}">{{.UnscannedClusters}}</span>
                <span class="health-label">未巡检集群</span>
            </div>
        </div>

        <!-- Node Health summary -->
        <div class="health-summary" style="border-top: none;">
            <div class="health-card">
                <span class="health-score">{{.TotalNodes}}</span>
                <span class="health-label">总节点数</span>
                    </div>
            <div class="health-card">
                <span class="health-score health-good">{{.NormalNodes}}</span>
                <span class="health-label">健康节点</span>
            </div>
            <div class="health-card">
                {{if gt .AbnormalNodes 0}}
                <a href="#异常节点" style="text-decoration: none;">
                    <span class="health-score health-critical">{{.AbnormalNodes}}</span>
                    <span class="health-label">异常节点</span>
                </a>
                {{else}}
                <span class="health-score health-good">{{.AbnormalNodes}}</span>
                <span class="health-label">异常节点</span>
                {{end}}
            </div>
            <div class="health-card">
                {{if gt .MissingNodesCount 0}}
                <a href="#未巡检节点" style="text-decoration: none;">
                    <span class="health-score health-warning">{{.MissingNodesCount}}</span>
                    <span class="health-label">未巡检节点</span>
                </a>
                {{else}}
                <span class="health-score health-good">{{.MissingNodesCount}}</span>
                <span class="health-label">未巡检节点</span>
                {{end}}
            </div>
        </div>

        <div class="main-content">
            <!-- Critical Alert -->
            {{if gt .AbnormalNodes 0}}
            <div class="alert-box alert-critical">
                <div class="alert-icon">⚠️</div>
                <div class="alert-content">
                    <div class="alert-title">发现 {{.AbnormalNodes}} 个异常节点需要紧急处理</div>
                    <p class="alert-message">存在潜在安全风险，请尽快修复以下异常节点以确保系统安全。系统健康度: <strong>{{.NormalNodesPercent}}%</strong></p>
                </div>
            </div>
            {{end}}

            {{if gt .MissingNodesCount 0}}
            <div class="alert-box alert-warning">
                <div class="alert-icon">⚠️</div>
                <div class="alert-content">
                    <div class="alert-title">发现 {{.MissingNodesCount}} 个未巡检节点</div>
                    <p class="alert-message">这些节点未能完成安全检查，请检查节点网络连接或配置是否正确。巡检完成度: <strong>{{sub 100 (printf "%.0f" (add (add (toFloat64 .AbnormalNodesPercent) (toFloat64 .MissingNodesPercent)) 0))}}%</strong></p>
                </div>
            </div>
            {{end}}

            {{if and (eq .AbnormalNodes 0) (eq .MissingNodesCount 0)}}
            <div class="alert-box alert-good">
                <div class="alert-icon">✓</div>
                <div class="alert-content">
                    <div class="alert-title">系统状态良好</div>
                    <p class="alert-message">当前所有节点运行正常，系统健康度: <strong>100%</strong></p>
                </div>
                        </div>
            {{end}}

            <!-- Dashboard -->
            <div class="dashboard-grid">
                <!-- 异常类型分布图表 - 现在占据整个区域 -->
                <div class="dashboard-card" style="grid-column: span 2;" id="异常分布">
                    <div class="dashboard-card-header">异常类型分布</div>
                    <div class="dashboard-card-body">
                        <div class="chart-container">
                            <!-- 使用内联CSS创建的水平条形图，更适合邮件客户端 -->
                            <div style="max-width:700px; margin:0 auto; padding:10px 0;">
                                <h3 style="text-align:center; font-size:14px; color:#334155; margin-bottom:20px; font-weight:500;">按安全问题类型</h3>

                                {{if gt (len .CheckItemFailureSummary) 0}}
                                    <!-- 计算最大失败次数以便比例缩放 -->
                                    {{$maxFailures := 0}}
                                    {{range .CheckItemFailureSummary}}
                                        {{if gt .TotalFailures $maxFailures}}
                                            {{$maxFailures = .TotalFailures}}
                                        {{end}}
                                    {{end}}

                                    <!-- 绘制每个异常类型的条形 -->
                                    {{range $index, $item := .CheckItemFailureSummary}}
                                        <div style="display:flex; align-items:center; margin-bottom:10px;">
                                            <!-- 左侧标签 - 异常类型名称 -->
                                            <div style="width:45%; text-align:right; padding-right:10px; font-size:12px; color:#334155; overflow:hidden; text-overflow:ellipsis;">
                                                {{$item.ItemName}}
                                            </div>

                                            <!-- 条形图 -->
                                            {{$percentage := div (mul $item.TotalFailures 100) $maxFailures}}
                                            <div style="width:40%; padding:0 5px;">
                                                <div style="background-color:{{if eq $item.HeatColor "heat-level-high"}}#ef4444{{else if eq $item.HeatColor "heat-level-2"}}#f59e0b{{else}}#f97316{{end}};
                                                            height:18px; width:{{$percentage}}%; min-width:5px; border-radius:3px; opacity:0.85;"></div>
                                            </div>

                                            <!-- 右侧数值 -->
                                            <div style="width:15%; font-weight:500; font-size:12px; color:#334155;">
                                                {{$item.TotalFailures}}
                                            </div>
                                        </div>
                                    {{end}}
                                {{else}}
                                    <!-- 没有异常项时显示的内容 -->
                                    <div style="text-align:center; padding:30px 0;">
                                        <!-- 安全状态图标 -->
                                        <table cellpadding="0" cellspacing="0" border="0" style="margin:0 auto;">
                                            <tr>
                                                <td style="background-color:#dcfce7; width:80px; height:80px; border-radius:50%; text-align:center; vertical-align:middle;">
                                                    <span style="color:#10b981; font-size:36px; font-weight:bold;">&#10004;</span>
                                                </td>
                                            </tr>
                                        </table>

                                        <div style="margin-top:15px; font-weight:500; color:#10b981;">系统安全状态良好</div>
                                        <div style="margin-top:5px; color:#64748b; font-size:14px;">未发现安全问题</div>
                                    </div>
                                {{end}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {{if .ClusterHealthSummary}}
            <div class="section" id="集群健康">
                <div class="section-header">
                    <div class="section-title">
                        <div class="section-icon">🔍</div>
                        集群健康状态
                    </div>
                </div>

                <div class="dashboard-card" style="margin-bottom: 30px;">
                    <div class="dashboard-card-header">集群健康状态列表</div>
                    <table class="cluster-health-table">
                <thead>
                    <tr>
                        <th>集群名称</th>
                        <th>健康状态</th>
                        <th>异常节点数</th>
                                <th>正常节点数</th>
                                <th>未巡检节点数</th>
                        <th>失败检查项数</th>
                    </tr>
                </thead>
                <tbody>
                            {{ range .ClusterHealthSummary }}
                            <tr class="cluster-row"
                                data-cluster="{{ .ClusterName }}"
                                data-status="{{ .StatusColor }}"
                                data-is-healthy="{{ if (strEq .StatusColor "green") }}true{{ else }}false{{ end }}">
                                <td>{{ .ClusterName }}</td>
                                <td>
                                    <div class="status-indicator">
                                        <span class="status-dot {{ .StatusColor }}"></span>
                                        <span class="status-text">{{ if (strEq .StatusColor "red") }}异常{{ else }}正常{{ end }}</span>
                                    </div>
                        </td>
                        <td>
                                    {{ if gt .AbnormalNodes 0 }}
                                    <a href="#abnormal-{{.ClusterName}}" class="count-link">{{ .AbnormalNodes }}</a>
                                    {{ else }}
                                    {{ .AbnormalNodes }}
                                    {{ end }}
                                </td>
                                <td>{{ if .TotalNodes }}{{ .NormalNodes }}{{ else }}0{{ end }}</td>
                                <td>
                                    {{ if gt .MissingNodes 0 }}
                                    <a href="#missing-{{.ClusterName}}" class="count-link">{{ .MissingNodes }}</a>
                                    {{ else }}
                                    {{ .MissingNodes }}
                                    {{ end }}
                        </td>
                                <td>{{ .FailedChecks }}</td>
                    </tr>
                            {{ end }}
                </tbody>
            </table>
                </div>
        </div>
        {{end}}

            {{if .MissingNodes}}
            <div class="section" id="未巡检节点">
                    <div class="section-header">
                        <div class="section-title">
                            <div class="section-icon">⚠️</div>
                            巡检失败节点 <span class="count-badge">{{.MissingNodesCount}}个</span>
                        </div>
                    </div>

                    <div class="alert-box alert-critical" style="margin-top:0">
                        <div class="alert-content">
                            <p class="alert-message">以下节点未能完成巡检，请检查节点网络连接或配置是否正确。未巡检节点会导致集群状态被标记为异常（红色）。</p>
                        </div>
                    </div>

                    <!-- 按集群分组显示未巡检节点 -->
                    {{$currentCluster := ""}}
                    {{range $index, $node := .MissingNodes}}
                        {{if ne $currentCluster $node.ClusterName}}
                            {{if ne $index 0}}</tbody></table></div>{{end}}
                            <div id="missing-{{$node.ClusterName}}" class="missing-cluster-section">
                                <div class="cluster-header">
                                    <h3 class="cluster-title">
                                        <span class="toggle-icon">
                                            <span class="arrow up"></span>
                                        </span>
                                        集群: {{$node.ClusterName}}
                                    </h3>
                                </div>
                                <div id="missing-content-{{$node.ClusterName}}" class="cluster-content">
                                    <table class="data-table">
                <thead>
                    <tr>
                                                <th>节点类型</th>
                                                <th>节点名称</th>
                    </tr>
                </thead>
                <tbody>
                            {{$currentCluster = $node.ClusterName}}
                        {{end}}
                    <tr>
                            <td>{{$node.NodeType}}</td>
                            <td style="color: #ef4444; font-weight:500;">{{$node.NodeName}}</td>
                    </tr>
                    {{end}}
                    {{if .MissingNodes}}</tbody></table></div>{{end}}
        </div>
        </div>
        {{end}}

        {{if .AbnormalDetails}}
            <div class="section" id="异常节点">
                    <div class="section-header">
                        <div class="section-title">
                            <div class="section-icon">🚨</div>
                            高优先级异常详情 <span class="count-badge">{{.AbnormalNodes}}个</span>
                        </div>
                    </div>

                    <div class="alert-box alert-critical" style="margin-top:0">
                        <div class="alert-content">
                            <p class="alert-message">以下节点存在安全基线异常，请及时修复以确保集群安全。</p>
                        </div>
                    </div>

                    <!-- 按集群分组显示异常节点 -->
                    {{$currentCluster := ""}}
                    {{range $index, $node := .AbnormalDetails}}
                        {{if ne $currentCluster $node.ClusterName}}
                            {{if ne $index 0}}</div></div></div>{{end}}
                            <div id="abnormal-{{$node.ClusterName}}" class="abnormal-cluster-section">
                                <div class="cluster-header">
                                    <h3 class="cluster-title">
                                        <span class="toggle-icon">
                                            <span class="arrow up"></span>
                                        </span>
                                        集群: {{$node.ClusterName}}
                                        <span style="color:#ef4444; font-size:0.9em;">
                                            <!-- 计算同一集群的节点数量 -->
                                            (
                                            {{$count := 0}}
                                            {{range $.AbnormalDetails}}
                                                {{if eq .ClusterName $node.ClusterName}}
                                                    {{$count = add $count 1}}
                                                {{end}}
                                            {{end}}
                                            {{$count}}个异常节点)
                                        </span>
                                    </h3>
                                </div>
                                <div id="abnormal-content-{{$node.ClusterName}}" class="cluster-content">
                                    <div class="issue-cards">
                            {{$currentCluster = $node.ClusterName}}
                        {{end}}
                        <div class="issue-card" id="abnormal-node-{{$node.ClusterName}}-{{$node.NodeName}}">
                            <div class="issue-card-header">
                                <span class="issue-title">异常节点</span>
                                <span class="issue-location">{{$node.NodeName}}</span>
                            </div>
                            <div class="issue-card-body">
                                <div style="margin-bottom:12px; padding-bottom:12px; border-bottom:1px dashed #f1f5f9;">
                                    <div style="color:#64748b; font-size:0.9em; margin-bottom:4px;">节点类型</div>
                                    <div style="font-weight:500;">{{$node.NodeType}}</div>
                                </div>

                                {{range $node.FailedItems}}
                                <div class="issue-detail">
                                    <div class="issue-name">{{.ItemName}}</div>
                                    <div class="issue-value">{{.ItemValue}}</div>
                                    {{if .FixSuggestion}}
                                    <div class="issue-solution">修复建议: {{.FixSuggestion}}</div>
                                    {{else}}
                                    <div class="issue-solution">修复建议: 请联系安全运维团队</div>
                                    {{end}}
                                </div>
                                {{end}}
                            </div>
                        </div>
                    {{end}}
                    {{if .AbnormalDetails}}</div></div></div>{{end}}
                </div>
            {{end}}
        </div>

        <div class="footer">
            <div style="margin-bottom:8px;">本邮件由系统自动生成，请勿直接回复。</div>
            <div>安全基线巡检服务 © {{now | date "2006"}} </div>
        </div>
    </div>
</body>
</html>