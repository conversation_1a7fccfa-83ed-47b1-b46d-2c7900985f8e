<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            background: #f8fafc;
            color: #334155;
            margin: 0;
            padding: 0;
            line-height: 1.5;
        }
        .container {
            max-width: 840px;
            margin: 30px auto;
            background: #fff;
            border-radius: 14px;
            box-shadow: 0 4px 24px rgba(0,0,0,0.06);
            padding: 0;
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #334155 0%, #1e293b 100%);
            color: white;
            padding: 26px 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header-title {
            font-size: 1.6em;
            font-weight: 600;
            letter-spacing: 0.5px;
        }
        .header-date {
            font-size: 0.9em;
            opacity: 0.85;
        }
        .health-summary {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            border-bottom: 1px solid #e2e8f0;
        }
        .health-card {
            padding: 20px 24px;
            text-align: center;
            border-right: 1px solid #e2e8f0;
        }
        .health-card:last-child {
            border-right: none;
        }
        .health-score {
            font-size: 2.5em;
            font-weight: 700;
            display: block;
            margin-bottom: 6px;
        }
        .health-label {
            font-size: 0.95em;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: block;
            font-weight: 500;
        }
        .health-good { color: #10b981; }
        .health-warning { color: #f59e0b; }
        .health-critical { color: #ef4444; }

        .main-content {
            padding: 32px;
        }

        .alert-box {
            border-radius: 10px;
            padding: 20px 24px;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            gap: 20px;
        }
        .alert-icon {
            font-size: 2.2em;
            flex-shrink: 0;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }
        .alert-content {
            flex-grow: 1;
        }
        .alert-title {
            font-size: 1.3em;
            font-weight: 600;
            margin-bottom: 4px;
        }
        .alert-message {
            margin: 0;
            font-size: 0.95em;
        }
        .alert-critical {
            background-color: #fef2f2;
            border-left: 6px solid #ef4444;
        }
        .alert-critical .alert-icon {
            background-color: #fee2e2;
            color: #ef4444;
        }
        .alert-critical .alert-title {
            color: #b91c1c;
        }
        .alert-good {
            background-color: #f0fdf4;
            border-left: 6px solid #10b981;
        }
        .alert-good .alert-icon {
            background-color: #dcfce7;
            color: #10b981;
        }
        .alert-good .alert-title {
            color: #047857;
        }

        .alert-warning {
            background-color: #fffbeb;
            border-left: 6px solid #f59e0b;
        }
        .alert-warning .alert-icon {
            background-color: #fef3c7;
            color: #f59e0b;
        }
        .alert-warning .alert-title {
            color: #b45309;
        }

        .section {
            margin-bottom: 36px;
        }
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            border-bottom: 1px solid #e2e8f0;
            padding-bottom: 12px;
        }
        .section-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .section-icon {
            background: #f1f5f9;
            color: #64748b;
            border-radius: 6px;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9em;
        }

         
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 24px;
            margin-bottom: 36px;
        }
        .dashboard-card {
            background: #fff;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 2px 6px rgba(0,0,0,0.03);
            overflow: hidden;
        }
        .dashboard-card-header {
            padding: 14px 18px;
            font-weight: 600;
            color: #334155;
            font-size: 1em;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }
        .dashboard-card-body {
            padding: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

         
        .chart-container {
            width: 100%;
            height: 220px;
        }

         
        .data-table, .cluster-health-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 0;
            font-size: 0.95em;
        }
        .data-table th,
        .data-table td,
        .cluster-health-table th,
        .cluster-health-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        .data-table th,
        .cluster-health-table th {
            background-color: #f8fafc;
            font-weight: 600;
            color: #475569;
        }
        .data-table tr:last-child td,
        .cluster-health-table tr:last-child td {
            border-bottom: none;
        }
        .data-table td.highlight,
        .cluster-health-table td.highlight {
            font-weight: 500;
        }
        .cluster-row:hover {
            background-color: #f8fafc;
            transition: background-color 0.2s;
        }
        .cluster-row[data-is-healthy="true"] td:first-child {
            border-left: 3px solid #10b981;
        }
        .cluster-row[data-is-healthy="false"] td:first-child {
            border-left: 3px solid #ef4444;
        }

         
        .cluster-row[data-status="red"] td:first-child {
            border-left: 3px solid #ef4444;
        }
        .cluster-row[data-status="green"] td:first-child {
            border-left: 3px solid #10b981;
        }

         
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .status-dot {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
        }
        .status-dot.red {
            background-color: #ef4444;
        }
        .status-dot.green {
            background-color: #10b981;
        }
        .status-dot.yellow {
            background-color: #f59e0b;
        }
        .status-text {
            font-weight: 500;
        }

        .view-details-link {
            color: #3b82f6;
            text-decoration: none;
            font-weight: 500;
            display: inline-block;
            padding: 6px 12px;
            background-color: #eff6ff;
            border-radius: 4px;
            transition: all 0.2s;
        }
        .view-details-link:hover {
            text-decoration: none;
            background-color: #dbeafe;
            transform: translateY(-1px);
        }
        .no-details {
            color: #94a3b8;
            font-style: italic;
            display: inline-block;
            padding: 6px 12px;
        }

         
        .issue-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .issue-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid #f1f5f9;
            overflow: hidden;
        }
        .issue-card-header {
            background: #fef2f2;
            padding: 12px 16px;
            font-weight: 600;
            color: #b91c1c;
            border-bottom: 1px solid #fee2e2;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .issue-location {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .issue-card-body {
            padding: 16px;
        }
        .issue-detail {
            margin-bottom: 12px;
            padding-bottom: 12px;
            border-bottom: 1px dashed #f1f5f9;
        }
        .issue-detail:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        .issue-name {
            font-weight: 500;
            color: #334155;
            margin-bottom: 4px;
        }
        .issue-value {
            color: #ef4444;
            font-family: monospace;
            background: #fef2f2;
            padding: 3px 8px;
            border-radius: 4px;
            margin-bottom: 4px;
            font-size: 0.9em;
        }
        .issue-solution {
            color: #64748b;
            font-size: 0.9em;
            font-style: italic;
        }

         
        .action-button {
            display: inline-block;
            padding: 8px 20px;
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            border-radius: 8px;
            font-weight: 500;
            text-decoration: none;
            margin-top: 8px;
            box-shadow: 0 2px 6px rgba(37, 99, 235, 0.3);
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
        }
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
        }

         
        .footer {
            background: #f8fafc;
            padding: 20px 32px;
            color: #64748b;
            font-size: 0.9em;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }

         
        .heat-level-high {
            color: #ef4444;
            font-weight: 600;
        }
        .heat-level-2 {
            color: #f59e0b;
            font-weight: 500;
        }
        .heat-level-1 {
            color: #f97316;
         }

         
        .hidden-node {
            display: block !important;
        }

         
        .hidden-cluster {
            display: none;
        }
        .section-note {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
            font-style: italic;
        }
        .button-container {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 10px;
        }
        .view-all-button {
            display: flex;
            align-items: center;
            background: none;
            border: none;
            color: #4a90e2;
            cursor: pointer;
            font-size: 14px;
        }
        .arrow {
            display: inline-block;
            margin-left: 5px;
            border: solid #4a90e2;
            border-width: 0 2px 2px 0;
            padding: 3px;
            transition: transform 0.3s;
        }
        .arrow.down {
            transform: rotate(45deg);
        }
        .arrow.up {
            transform: rotate(-135deg);
        }

         
        .count-link {
            display: inline-block;
            color: #3b82f6;
            font-weight: 600;
            text-decoration: none;
            padding: 2px 8px;
            border-radius: 4px;
            background-color: #eff6ff;
            transition: all 0.2s;
        }
        .count-link:hover {
            background-color: #dbeafe;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
        }

         
        .missing-cluster-section {
            margin-bottom: 24px;
        }
        .cluster-title {
            color: #334155;
            font-size: 1.1em;
            margin-bottom: 12px;
            padding-bottom: 6px;
            border-bottom: 1px dashed #e2e8f0;
        }

         
        .cluster-header {
            cursor: pointer;
        }
        .cluster-content {
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        .cluster-content.hidden {
            display: block !important;
        }
        .toggle-icon {
            display: inline-block;
            width: 20px;
            height: 20px;
            margin-right: 5px;
            cursor: pointer;
            position: relative;
            top: 2px;
         }

         
        .hidden, .hidden-node {
            display: block !important;
        }
        .cluster-content.hidden {
            display: block !important;
        }

         
        @media (max-width: 768px) {
             
            .container {
                margin: 0;
                border-radius: 0;
                width: 100%;
                max-width: 100%;
                box-shadow: none;
            }

             
            .header {
                padding: 20px;
                flex-direction: column;
                align-items: flex-start;
            }
            .header-title {
                font-size: 1.3em;
                margin-bottom: 8px;
            }

             
            .health-summary {
                grid-template-columns: 1fr 1fr;
            }
            .health-card {
                padding: 15px;
                border-bottom: 1px solid #e2e8f0;
            }
            .health-card:nth-child(odd) {
                border-right: 1px solid #e2e8f0;
            }
            .health-card:nth-child(even) {
                border-right: none;
            }
            .health-card:nth-child(3),
            .health-card:nth-child(4) {
                border-bottom: none;
            }
            .health-score {
                font-size: 2em;
            }

             
            .main-content {
                padding: 20px 15px;
            }

             
            .alert-box {
                padding: 15px;
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }
            .alert-icon {
                width: 50px;
                height: 50px;
                margin: 0 auto;
            }
            .alert-title {
                font-size: 1.1em;
            }

             
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

             
            .cluster-health-table {
                display: block;
                overflow-x: auto;
                width: 100%;
                -webkit-overflow-scrolling: touch;
                font-size: 0.85em;
            }
            .cluster-health-table th,
            .cluster-health-table td {
                padding: 10px 12px;
                white-space: nowrap;
            }
             
            .cluster-row[data-is-healthy="true"] td:first-child,
            .cluster-row[data-is-healthy="false"] td:first-child,
            .cluster-row[data-status="red"] td:first-child,
            .cluster-row[data-status="green"] td:first-child {
                border-left-width: 3px;
            }

             
            .issue-cards {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            .issue-card {
                margin-bottom: 0;
            }

             
            .missing-cluster-section {
                margin-bottom: 15px;
            }

             
            .data-table {
                display: block;
                overflow-x: auto;
                width: 100%;
                -webkit-overflow-scrolling: touch;
                font-size: 0.85em;
            }

             
            .cluster-title {
                font-size: 1em;
                word-break: break-word;
            }

             
            .count-link {
                padding: 2px 6px;
                font-size: 0.9em;
            }

             
            .section-title {
                font-size: 1.1em;
                flex-wrap: wrap;
            }
            .section-icon {
                width: 24px;
                height: 24px;
            }
        }

         
        @media (max-width: 375px) {
            .health-card {
                padding: 10px;
            }
            .health-score {
                font-size: 1.8em;
            }
            .main-content {
                padding: 15px 10px;
            }
            .issue-card-header {
                flex-direction: column;
                align-items: flex-start;
            }
            .issue-location {
                margin-top: 4px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-title">节点安全基线巡检报告</div>
            <div class="header-date">生成时间: 2025-04-29 08:58:28</div>
                </div>

        
        
        <div class="health-summary">
            <div class="health-card">
                <span class="health-score">3</span>
                <span class="health-label">总集群数</span>
            </div>
            <div class="health-card">
                <span class="health-score health-good">1</span>
                <span class="health-label">正常集群</span>
                </div>
            <div class="health-card">
                <span class="health-score health-good">0</span>
                <span class="health-label">未巡检集群</span>
            </div>
        </div>

        
        <div class="health-summary" style="border-top: none;">
            <div class="health-card">
                <span class="health-score">12</span>
                <span class="health-label">总节点数</span>
                    </div>
            <div class="health-card">
                <span class="health-score health-good">9</span>
                <span class="health-label">健康节点</span>
            </div>
            <div class="health-card">
                
                <a href="#异常节点" style="text-decoration: none;">
                    <span class="health-score health-critical">3</span>
                    <span class="health-label">异常节点</span>
                </a>
                
            </div>
            <div class="health-card">
                
                <span class="health-score health-good"></span>
                <span class="health-label">未巡检节点</span>
                
            </div>
        </div>

        <div class="main-content">
            
            
            <div class="alert-box alert-critical">
                <div class="alert-icon">⚠️</div>
                <div class="alert-content">
                    <div class="alert-title">发现 3 个异常节点需要紧急处理</div>
                    <p class="alert-message">存在潜在安全风险，请尽快修复以下异常节点以确保系统安全。系统健康度: <strong>75%</strong></p>
                </div>
            </div>
            

            

            

            
            <div class="dashboard-grid">
                
                <div class="dashboard-card" style="grid-column: span 2;" id="异常分布">
                    <div class="dashboard-card-header">异常类型分布</div>
                    <div class="dashboard-card-body">
                        <div class="chart-container">
                            
                            <div style="max-width:700px; margin:0 auto; padding:10px 0;">
                                <h3 style="text-align:center; font-size:14px; color:#334155; margin-bottom:20px; font-weight:500;">按安全问题类型</h3>

                                
                                    
                                    
                                    
                                        
                                            
                                        
                                    
                                        
                                    
                                        
                                    
                                        
                                    

                                    
                                    
                                        <div style="display:flex; align-items:center; margin-bottom:10px;">
                                            
                                            <div style="width:45%; text-align:right; padding-right:10px; font-size:12px; color:#334155; overflow:hidden; text-overflow:ellipsis;">
                                                未授权访问风险
                                            </div>

                                            
                                            
                                            <div style="width:40%; padding:0 5px;">
                                                <div style="background-color:#ef4444;
                                                            height:18px; width:100%; min-width:5px; border-radius:3px; opacity:0.85;"></div>
                                            </div>

                                            
                                            <div style="width:15%; font-weight:500; font-size:12px; color:#334155;">
                                                3
                                            </div>
                                        </div>
                                    
                                        <div style="display:flex; align-items:center; margin-bottom:10px;">
                                            
                                            <div style="width:45%; text-align:right; padding-right:10px; font-size:12px; color:#334155; overflow:hidden; text-overflow:ellipsis;">
                                                密码强度不足
                                            </div>

                                            
                                            
                                            <div style="width:40%; padding:0 5px;">
                                                <div style="background-color:#ef4444;
                                                            height:18px; width:66.66666666666667%; min-width:5px; border-radius:3px; opacity:0.85;"></div>
                                            </div>

                                            
                                            <div style="width:15%; font-weight:500; font-size:12px; color:#334155;">
                                                2
                                            </div>
                                        </div>
                                    
                                        <div style="display:flex; align-items:center; margin-bottom:10px;">
                                            
                                            <div style="width:45%; text-align:right; padding-right:10px; font-size:12px; color:#334155; overflow:hidden; text-overflow:ellipsis;">
                                                防火墙配置不当
                                            </div>

                                            
                                            
                                            <div style="width:40%; padding:0 5px;">
                                                <div style="background-color:#ef4444;
                                                            height:18px; width:33.333333333333336%; min-width:5px; border-radius:3px; opacity:0.85;"></div>
                                            </div>

                                            
                                            <div style="width:15%; font-weight:500; font-size:12px; color:#334155;">
                                                1
                                            </div>
                                        </div>
                                    
                                        <div style="display:flex; align-items:center; margin-bottom:10px;">
                                            
                                            <div style="width:45%; text-align:right; padding-right:10px; font-size:12px; color:#334155; overflow:hidden; text-overflow:ellipsis;">
                                                日志审计缺失
                                            </div>

                                            
                                            
                                            <div style="width:40%; padding:0 5px;">
                                                <div style="background-color:#ef4444;
                                                            height:18px; width:33.333333333333336%; min-width:5px; border-radius:3px; opacity:0.85;"></div>
                                            </div>

                                            
                                            <div style="width:15%; font-weight:500; font-size:12px; color:#334155;">
                                                1
                                            </div>
                                        </div>
                                    
                                
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            
            <div class="section" id="集群健康">
                <div class="section-header">
                    <div class="section-title">
                        <div class="section-icon">🔍</div>
                        集群健康状态
                    </div>
                </div>

                <div class="dashboard-card" style="margin-bottom: 30px;">
                    <div class="dashboard-card-header">集群健康状态列表</div>
                    <table class="cluster-health-table">
                <thead>
                    <tr>
                        <th>集群名称</th>
                        <th>健康状态</th>
                        <th>异常节点数</th>
                                <th>正常节点数</th>
                                <th>未巡检节点数</th>
                        <th>失败检查项数</th>
                    </tr>
                </thead>
                <tbody>
                            
                            <tr class="cluster-row"
                                data-cluster="集群A"
                                data-status="red"
                                data-is-healthy="false">
                                <td>集群A</td>
                                <td>
                                    <div class="status-indicator">
                                        <span class="status-dot red"></span>
                                        <span class="status-text">异常</span>
                                    </div>
                        </td>
                        <td>
                                    
                                    <a href="#abnormal-%e9%9b%86%e7%be%a4A" class="count-link">1</a>
                                    
                                </td>
                                <td></td>
                                <td>
                                    
                                    
                                    
                        </td>
                                <td>2</td>
                    </tr>
                            
                            <tr class="cluster-row"
                                data-cluster="集群B"
                                data-status="red"
                                data-is-healthy="false">
                                <td>集群B</td>
                                <td>
                                    <div class="status-indicator">
                                        <span class="status-dot red"></span>
                                        <span class="status-text">异常</span>
                                    </div>
                        </td>
                        <td>
                                    
                                    <a href="#abnormal-%e9%9b%86%e7%be%a4B" class="count-link">2</a>
                                    
                                </td>
                                <td></td>
                                <td>
                                    
                                    
                                    
                        </td>
                                <td>2</td>
                    </tr>
                            
                            <tr class="cluster-row"
                                data-cluster="集群C"
                                data-status="green"
                                data-is-healthy="true">
                                <td>集群C</td>
                                <td>
                                    <div class="status-indicator">
                                        <span class="status-dot green"></span>
                                        <span class="status-text">正常</span>
                                    </div>
                        </td>
                        <td>
                                    
                                    0
                                    
                                </td>
                                <td></td>
                                <td>
                                    
                                    
                                    
                        </td>
                                <td>0</td>
                    </tr>
                            
                </tbody>
            </table>
                </div>
        </div>
        

            
            <div class="section" id="未巡检节点">
                    <div class="section-header">
                        <div class="section-title">
                            <div class="section-icon">⚠️</div>
                            巡检失败节点 <span class="count-badge">个</span>
                        </div>
                    </div>

                    <div class="alert-box alert-critical" style="margin-top:0">
                        <div class="alert-content">
                            <p class="alert-message">以下节点未能完成巡检，请检查节点网络连接或配置是否正确。未巡检节点会导致集群状态被标记为异常（红色）。</p>
                        </div>
                    </div>

                    
                    
                    
                        
                            
                            <div id="missing-集群A" class="missing-cluster-section">
                                <div class="cluster-header">
                                    <h3 class="cluster-title">
                                        <span class="toggle-icon">
                                            <span class="arrow up"></span>
                                        </span>
                                        集群: 集群A
                                    </h3>
                                </div>
                                <div id="missing-content-集群A" class="cluster-content">
                                    <table class="data-table">
                <thead>
                    <tr>
                                                <th>节点类型</th>
                                                <th>节点名称</th>
                    </tr>
                </thead>
                <tbody>
                            
                        
                    <tr>
                            <td>数据库节点</td>
                            <td style="color: #ef4444; font-weight:500;">db-slave-02</td>
                    </tr>
                    
                    </tbody></table></div>
        </div>
        </div>
        

        
            <div class="section" id="异常节点">
                    <div class="section-header">
                        <div class="section-title">
                            <div class="section-icon">🚨</div>
                            高优先级异常详情 <span class="count-badge">3个</span>
                        </div>
                    </div>

                    <div class="alert-box alert-critical" style="margin-top:0">
                        <div class="alert-content">
                            <p class="alert-message">以下节点存在安全基线异常，请及时修复以确保集群安全。</p>
                        </div>
                    </div>

                    
                    
                    
                        
                            
                            <div id="abnormal-集群A" class="abnormal-cluster-section">
                                <div class="cluster-header">
                                    <h3 class="cluster-title">
                                        <span class="toggle-icon">
                                            <span class="arrow up"></span>
                                        </span>
                                        集群: 集群A
                                        <span style="color:#ef4444; font-size:0.9em;">
                                            
                                            (
                                            
                                            
                                                
                                                    
                                                
                                            
                                                
                                                    
                                                
                                            
                                                
                                                    
                                                
                                            
                                            3个异常节点)
                                        </span>
                                    </h3>
                                </div>
                                <div id="abnormal-content-集群A" class="cluster-content">
                                    <div class="issue-cards">
                            
                        
                        <div class="issue-card" id="abnormal-node-集群A-app-server-01">
                            <div class="issue-card-header">
                                <span class="issue-title">异常节点</span>
                                <span class="issue-location">app-server-01</span>
                            </div>
                            <div class="issue-card-body">
                                <div style="margin-bottom:12px; padding-bottom:12px; border-bottom:1px dashed #f1f5f9;">
                                    <div style="color:#64748b; font-size:0.9em; margin-bottom:4px;">节点类型</div>
                                    <div style="font-weight:500;">应用服务器</div>
                                </div>

                                
                                <div class="issue-detail">
                                    <div class="issue-name">未授权访问风险</div>
                                    <div class="issue-value">发现8个敏感端口开放</div>
                                    
                                    <div class="issue-solution">修复建议: 关闭不必要的端口，配置IP白名单</div>
                                    
                                </div>
                                
                                <div class="issue-detail">
                                    <div class="issue-name">密码强度不足</div>
                                    <div class="issue-value">管理员密码仅8位，未包含特殊字符</div>
                                    
                                    <div class="issue-solution">修复建议: 更新密码策略，要求至少12位且包含大小写字母、数字和特殊字符</div>
                                    
                                </div>
                                
                            </div>
                        </div>
                    
                        
                            </div></div></div>
                            <div id="abnormal-集群B" class="abnormal-cluster-section">
                                <div class="cluster-header">
                                    <h3 class="cluster-title">
                                        <span class="toggle-icon">
                                            <span class="arrow up"></span>
                                        </span>
                                        集群: 集群B
                                        <span style="color:#ef4444; font-size:0.9em;">
                                            
                                            (
                                            
                                            
                                                
                                                    
                                                
                                            
                                                
                                                    
                                                
                                            
                                                
                                                    
                                                
                                            
                                            3个异常节点)
                                        </span>
                                    </h3>
                                </div>
                                <div id="abnormal-content-集群B" class="cluster-content">
                                    <div class="issue-cards">
                            
                        
                        <div class="issue-card" id="abnormal-node-集群B-lb-master-01">
                            <div class="issue-card-header">
                                <span class="issue-title">异常节点</span>
                                <span class="issue-location">lb-master-01</span>
                            </div>
                            <div class="issue-card-body">
                                <div style="margin-bottom:12px; padding-bottom:12px; border-bottom:1px dashed #f1f5f9;">
                                    <div style="color:#64748b; font-size:0.9em; margin-bottom:4px;">节点类型</div>
                                    <div style="font-weight:500;">负载均衡器</div>
                                </div>

                                
                                <div class="issue-detail">
                                    <div class="issue-name">防火墙配置不当</div>
                                    <div class="issue-value">过于宽松的入站规则</div>
                                    
                                    <div class="issue-solution">修复建议: 限制入站流量仅来自已知IP地址</div>
                                    
                                </div>
                                
                            </div>
                        </div>
                    
                        
                        <div class="issue-card" id="abnormal-node-集群B-app-server-03">
                            <div class="issue-card-header">
                                <span class="issue-title">异常节点</span>
                                <span class="issue-location">app-server-03</span>
                            </div>
                            <div class="issue-card-body">
                                <div style="margin-bottom:12px; padding-bottom:12px; border-bottom:1px dashed #f1f5f9;">
                                    <div style="color:#64748b; font-size:0.9em; margin-bottom:4px;">节点类型</div>
                                    <div style="font-weight:500;">应用服务器</div>
                                </div>

                                
                                <div class="issue-detail">
                                    <div class="issue-name">日志审计缺失</div>
                                    <div class="issue-value">未启用关键操作审计日志</div>
                                    
                                    <div class="issue-solution">修复建议: 配置审计日志，并将日志发送到中央日志服务器</div>
                                    
                                </div>
                                
                            </div>
                        </div>
                    
                    </div></div></div>
                </div>
            
        </div>

        <div class="footer">
            <div style="margin-bottom:8px;">本邮件由系统自动生成，请勿直接回复。</div>
            <div>安全基线巡检服务 © 2025 </div>
        </div>
    </div>
</body>
</html>