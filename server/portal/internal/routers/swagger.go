// Package routers defines the HTTP routes for the portal module.
package routers

import (
	"github.com/gin-gonic/gin"
	swaggerfiles "github.com/swaggo/files"     // swagger embed files
	ginswagger "github.com/swaggo/gin-swagger" // gin-swagger middleware
	_ "navy-ng/docs"                           // docs is generated by Swag CLI, needs blank import.
)

// RegisterSwaggerRoutes registers swagger routes.
func RegisterSwaggerRoutes(r *gin.Engine) {
	// use ginSwagger middleware to serve the API docs
	r.GET("/swagger/*any", BasicAuthMiddleware(), ginswagger.WrapHandler(swaggerfiles.Handler))
}
