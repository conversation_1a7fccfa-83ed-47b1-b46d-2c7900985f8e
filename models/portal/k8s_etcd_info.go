package portal

import (
	"gorm.io/gorm"
)

// K8sEtcdInfo represents the etcd statistics table.
type K8sEtcdInfo struct {
	BaseModel
	DeletedAt                  gorm.DeletedAt `gorm:"index;comment:删除时间"`
	Instance                   string         `gorm:"type:varchar(191);comment:实例IP"`
	Job                        string         `gorm:"type:varchar(191);comment:普米采集job"`
	ServerID                   string         `gorm:"type:varchar(191);comment:服务序号"`
	HasLeader                  string         `gorm:"type:varchar(191);comment:是否有leader"`
	ChangesLeader              string         `gorm:"type:varchar(191);comment:是否更换过leader"`
	DBTotalSize                string         `gorm:"type:varchar(191);comment:数据库总大小"`
	ProcessMemory              string         `gorm:"type:varchar(191);comment:内存使用"`
	NetworkPeerReceivedSumRate string         `gorm:"type:varchar(191);comment:数据接收速率"`
	NetworkPeerSentSumRate     string         `gorm:"type:varchar(191);comment:数据发送速率"`
	NetworkGrpcReceivedRate    string         `gorm:"type:varchar(191);comment:grpc接收速率"`
	NetworkGrpcSentRate        string         `gorm:"type:varchar(191);comment:grpc发送速率"`                                   // Assuming comment based on pattern
	ProposalsFailedRate        string         `gorm:"type:varchar(191);comment:提案失败速率"`                                     // Correcting comment based on name
	ProposalsPendingRate       string         `gorm:"column:proposals_pengding_rate;type:varchar(191);comment:提案pending速率"` // Fixing typo in column name
	ProposalsCommittedRate     string         `gorm:"type:varchar(191);comment:提案提交速率"`
	ProposalsAppliedRate       string         `gorm:"type:varchar(191);comment:提案生效速率"`
	K8sClusterID               *uint64        `gorm:"comment:所属集群ID"` // Use pointer for nullable foreign key
}

// TableName overrides the default table name generated by GORM
func (K8sEtcdInfo) TableName() string {
	return "k8s_etcd_info"
}
