<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>【弹性伸缩】入池变更通知 - 订单号：ESO20241201123456</title>
</head>
<body style="margin: 0; padding: 20px; font-family: 'Microsoft YaHei', Arial, sans-serif; background-color: #f5f7fa;">
    <div style="max-width: 800px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
        <!-- 邮件头部 -->
        <div style="background: linear-gradient(135deg, #52c41a 0%, #52c41a 100%); color: white; padding: 24px; border-radius: 8px 8px 0 0;">
            <h1 style="margin: 0; font-size: 24px; font-weight: 600;">
                ⬆️ 入池变更通知
            </h1>
            <p style="margin: 8px 0 0 0; font-size: 14px; opacity: 0.9;">
                订单号：ESO20241201123456 | 创建时间：2024-12-01 14:30:25
            </p>
        </div>
        <!-- 邮件正文 -->
        <div style="padding: 32px;">
            <!-- 问候语 -->
            <div style="margin-bottom: 24px;">
                <h2 style="color: #333; font-size: 18px; margin: 0 0 12px 0;">👋 值班同事，您好！</h2>
                <p style="color: #666; font-size: 14px; line-height: 1.6; margin: 0;">
                    系统检测到集群资源需要进行<strong style="color: #52c41a;">入池</strong>变更操作，请及时处理相关工作。
                </p>
            </div>

            <!-- 变更详情 -->
            <div style="background-color: #f8f9fa; border-radius: 6px; padding: 20px; margin-bottom: 24px;">
                <h3 style="color: #333; font-size: 16px; margin: 0 0 16px 0;">📋 变更详情</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                    <div>
                        <span style="color: #666; font-size: 13px;">目标集群：</span>
                        <strong style="color: #333; font-size: 14px;">production-k8s-cluster</strong>
                    </div>
                    <div>
                        <span style="color: #666; font-size: 13px;">变更类型：</span>
                        <strong style="color: #52c41a; font-size: 14px;">入池</strong>
                    </div>
                    <div>
                        <span style="color: #666; font-size: 13px;">设备数量：</span>
                        <strong style="color: #333; font-size: 14px;">3 台</strong>
                    </div>
                    <div>
                        <span style="color: #666; font-size: 13px;">创建人：</span>
                        <strong style="color: #333; font-size: 14px;">admin</strong>
                    </div>
                </div>
            </div>

            <!-- 设备信息 -->
            <div style="margin-bottom: 24px;">
                <h3 style="color: #333; font-size: 16px; margin: 0 0 16px 0;">🖥️ 涉及设备</h3>
                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse; background-color: white; border-radius: 6px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                        <thead>
                            <tr style="background-color: #f1f3f4;">
                                <th style="padding: 12px 8px; border: 1px solid #e0e0e0; font-size: 13px; font-weight: 600; color: #333;">CI编码</th>
                                <th style="padding: 12px 8px; border: 1px solid #e0e0e0; font-size: 13px; font-weight: 600; color: #333;">IP地址</th>
                                <th style="padding: 12px 8px; border: 1px solid #e0e0e0; font-size: 13px; font-weight: 600; color: #333;">架构</th>
                                <th style="padding: 12px 8px; border: 1px solid #e0e0e0; font-size: 13px; font-weight: 600; color: #333;">CPU</th>
                                <th style="padding: 12px 8px; border: 1px solid #e0e0e0; font-size: 13px; font-weight: 600; color: #333;">内存</th>
                                <th style="padding: 12px 8px; border: 1px solid #e0e0e0; font-size: 13px; font-weight: 600; color: #333;">状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="padding: 8px; border: 1px solid #e0e0e0; text-align: center;">CI001</td>
                                <td style="padding: 8px; border: 1px solid #e0e0e0; text-align: center;">************</td>
                                <td style="padding: 8px; border: 1px solid #e0e0e0; text-align: center;">x86_64</td>
                                <td style="padding: 8px; border: 1px solid #e0e0e0; text-align: center;">8.0核</td>
                                <td style="padding: 8px; border: 1px solid #e0e0e0; text-align: center;">16.0GB</td>
                                <td style="padding: 8px; border: 1px solid #e0e0e0; text-align: center;">active</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px; border: 1px solid #e0e0e0; text-align: center;">CI002</td>
                                <td style="padding: 8px; border: 1px solid #e0e0e0; text-align: center;">192.168.1.11</td>
                                <td style="padding: 8px; border: 1px solid #e0e0e0; text-align: center;">x86_64</td>
                                <td style="padding: 8px; border: 1px solid #e0e0e0; text-align: center;">16.0核</td>
                                <td style="padding: 8px; border: 1px solid #e0e0e0; text-align: center;">32.0GB</td>
                                <td style="padding: 8px; border: 1px solid #e0e0e0; text-align: center;">active</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px; border: 1px solid #e0e0e0; text-align: center;">CI003</td>
                                <td style="padding: 8px; border: 1px solid #e0e0e0; text-align: center;">192.168.1.12</td>
                                <td style="padding: 8px; border: 1px solid #e0e0e0; text-align: center;">arm64</td>
                                <td style="padding: 8px; border: 1px solid #e0e0e0; text-align: center;">12.0核</td>
                                <td style="padding: 8px; border: 1px solid #e0e0e0; text-align: center;">24.0GB</td>
                                <td style="padding: 8px; border: 1px solid #e0e0e0; text-align: center;">active</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- 操作指引 -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 6px; padding: 20px; margin-bottom: 24px; color: white;">
                <h3 style="margin: 0 0 12px 0; font-size: 16px;">⚡ 处理指引</h3>
                <ul style="margin: 0; padding-left: 20px; line-height: 1.8;">
                    <li>请登录弹性伸缩管理系统查看订单详情</li>
                    <li>确认入池操作的设备列表和配置信息</li>
                    <li>按照标准操作流程执行入池变更</li>
                    <li>完成后请及时更新订单状态</li>
                </ul>
            </div>

            <!-- 重要提醒 -->
            <div style="border-left: 4px solid #ff4d4f; background-color: #fff2f0; padding: 16px; margin-bottom: 24px;">
                <h4 style="color: #cf1322; margin: 0 0 8px 0; font-size: 14px;">⚠️ 重要提醒</h4>
                <p style="color: #a8071a; font-size: 13px; line-height: 1.6; margin: 0;">
                    请在收到通知后<strong>30分钟内</strong>开始处理，如遇问题请及时联系相关技术人员。
                    变更过程中请严格按照操作规范执行，确保集群稳定性。
                </p>
            </div>

            <!-- 联系信息 -->
            <div style="text-align: center; padding: 20px; background-color: #f8f9fa; border-radius: 6px;">
                <p style="color: #666; font-size: 13px; margin: 0 0 8px 0;">
                    如有疑问，请联系系统管理员或查看操作文档
                </p>
                <p style="color: #999; font-size: 12px; margin: 0;">
                    此邮件由弹性伸缩系统自动发送，请勿直接回复
                </p>
            </div>
        </div>

        <!-- 邮件底部 -->
        <div style="background-color: #f1f3f4; padding: 16px 32px; border-radius: 0 0 8px 8px; text-align: center;">
            <p style="color: #666; font-size: 12px; margin: 0;">
                © 2024 弹性伸缩管理系统 | 发送时间：2024-12-01 14:30:25
            </p>
        </div>
    </div>
</body>
</html>
