# 弹性伸缩系统用户手册

## 1. 引言

本文档旨在帮助用户理解和使用弹性伸缩系统的各项功能，包括如何配置监控策略、设备匹配策略、手动创建订单以及了解订单的流转过程。

## 2. 核心功能概览

弹性伸缩系统主要提供以下核心功能：

- **智能监控与自动伸缩**：根据预设的监控策略，自动评估集群资源使用情况，并在满足条件时触发自动化的入池（扩容）或退池（缩容）操作。
- **灵活的设备匹配**：支持配置设备匹配策略，确保在伸缩操作时能够选择到最合适的设备。
- **订单化管理**：所有的伸缩操作都将生成订单，方便追踪和管理。
- **手动干预能力**：除了自动伸缩，系统也支持用户根据实际需求手动创建伸缩订单。
- **清晰的流转追踪**：提供订单状态的实时更新和历史记录，方便用户了解每个伸缩操作的完整生命周期。

## 3. 监控策略配置

监控策略是弹性伸缩系统自动触发伸缩操作的基础。用户可以定义策略来监控特定集群的资源使用情况（如CPU、内存），并设定阈值和持续时间，当资源使用达到预设条件时，系统将自动执行相应的入池或退池动作。

### 3.1 如何配置监控策略

1.  访问 **弹性伸缩 > 策略管理** 页面。
2.  点击“新建策略”或选择现有策略进行编辑。
3.  **基本信息**：
    *   **策略名称**：为策略指定一个清晰易懂的名称。
    *   **目标集群**：选择该策略需要监控的K8s集群。
    *   **策略类型**：选择“入池”或“退池”。
4.  **触发条件**：
    *   **监控指标**：选择要监控的资源指标，例如 `CPU使用率`、`内存使用率`。
    *   **阈值**：设置触发伸缩操作的资源使用阈值（例如，CPU使用率 > 80%）。
    *   **持续时间**：设置资源使用持续满足阈值条件多久后才触发操作（例如，连续 5 分钟）。
5.  **执行动作**（根据策略类型不同而不同）：
    *   **入池策略**：
        *   **期望设备数量**：定义触发入池操作时，期望增加的设备数量。
        *   **设备匹配策略**：选择用于挑选入池设备的匹配策略（详见第4节）。
    *   **退池策略**：
        *   **期望退回设备数量**：定义触发退池操作时，期望退回的设备数量。
        *   **设备选择偏好**：例如优先退回低负载设备等（具体选项视系统实现）。
6.  **启用策略**：确保策略处于“启用”状态才能被系统自动执行。
7.  保存策略配置。

### 3.2 查看策略执行历史

在策略管理页面，可以查看每个策略的执行历史，了解其触发时间、执行结果等信息。

## 4. 设备匹配策略配置

设备匹配策略用于在执行入池操作时，从可用的设备资源池中筛选出符合特定条件的设备。这有助于确保新加入集群的设备能够满足业务需求。

### 4.1 如何配置设备匹配策略

设备匹配策略通常与“入池”类型的监控策略关联，或在手动创建入池订单时指定。

1.  系统可能预置了一些常用的设备匹配模板（例如，按特定标签、机型、配置等筛选）。
2.  在创建或编辑“入池”监控策略时，在“执行动作”部分可以选择或配置设备匹配逻辑。
3.  **配置选项可能包括**：
    *   **资源池范围**：指定从哪些资源池中选择设备。
    *   **设备标签**：要求设备必须包含或不包含某些特定的标签。
    *   **硬件配置**：例如CPU核数、内存大小、是否有GPU等。
    *   **查询模板**：系统可能支持使用预定义的查询模板ID (`EntryQueryTemplateID`, `ExitQueryTemplateID`) 来进行更复杂的设备筛选。
    *   **匹配算法**：例如贪心选择算法，尽可能多地匹配符合条件的设备，直到满足期望数量。

### 4.2 注意事项

-   确保设备匹配策略的条件不会过于严苛导致无法匹配到任何设备。
-   如果系统在执行入池操作时无法根据匹配策略找到足够的设备，可能会创建部分满足的订单，或创建提醒订单提示资源不足。

## 5. 手动创建订单

除了依赖自动监控策略，用户也可以根据即时需求手动创建入池或退池订单。

### 5.1 如何手动创建订单

1.  访问 **弹性伸缩 > 订单管理** 页面。
2.  点击“创建订单”按钮。
3.  **订单类型**：选择“入池”或“退池”。
4.  **目标集群**：选择订单操作的目标K8s集群。
5.  **设备数量**：指定希望入池或退池的设备数量。
6.  **设备匹配/选择**：
    *   **入池订单**：需要指定设备匹配策略或条件，系统将根据此条件从可用资源中选择设备。
    *   **退池订单**：需要指定从目标集群中选择哪些设备进行退池。系统可能会列出当前集群中的设备供用户选择。
7.  **其他选项**：
    *   **备注**：可以为订单添加备注信息。
8.  确认并提交订单。

### 5.2 手动订单与自动订单的区别

-   手动订单由用户直接发起，用于应对计划外的或紧急的资源调整需求。
-   自动订单由监控策略根据预设条件自动触发。
-   两类订单在后续的流转和处理上通常遵循相同的流程。

## 6. 订单流转说明

无论是自动触发还是手动创建的订单，都会经历一系列的状态流转，直至最终完成或失败。

### 6.1 订单状态

一个典型的订单可能包含以下状态：

-   **待处理 (Pending)**：订单已创建，等待系统处理。
-   **处理中 (Processing)**：系统正在执行订单操作，例如匹配设备、配置节点等。
-   **已完成 (Completed)**：订单操作成功完成。
-   **已取消 (Cancelled)**：订单被用户或系统取消。
-   **归还中 (Returning)**：设备正在归还中。
-   **已归还 (Returned)**：设备已成功归还。
-   **无须归还 (NoReturnRequired)**：订单类型无须归还设备。

### 6.2 订单流转图示 (简化)

```mermaid
graph TD
    A[创建订单 (手动/自动)] --> B{待处理};
    B --> C{处理中};
    C -- 设备匹配/操作成功 --> D[已完成];
    C -- 部分设备成功 --> E[部分完成];
    C -- 匹配失败/操作失败 --> F[失败];
    C -- 需要人工介入 --> G[协调处理];
    B -- 用户取消 --> H[已取消];
    E -- 后续处理完成 --> D;
    E -- 后续处理失败 --> F;
```

### 6.3 查看订单详情与日志

用户可以在 **弹性伸缩 > 订单管理** 页面查看所有订单的列表及其当前状态。
点击特定订单可以进入订单详情页面，查看更详细的信息，包括：

-   订单基本信息（类型、目标集群、设备数量等）
-   关联的设备列表（`OrderDevice`）
-   详细的执行日志或步骤（`ElasticScalingOrderDetail`）
-   任何错误信息或警告

## 7. 常见问题 (FAQ)

-   **Q: 监控策略触发了，但为什么没有生成订单？**
    A: 请检查策略的执行历史，确认是否有错误信息。可能的原因包括：设备匹配策略无法找到可用设备、系统内部错误等。

-   **Q: 手动创建入池订单时，提示“无可用设备”怎么办？**
    A: 这意味着根据您指定的匹配条件，当前资源池中没有符合要求的设备。您可以尝试放宽匹配条件，或者联系资源管理员补充设备。

-   **Q: 订单状态长时间处于“处理中”怎么办？**
    A: 请查看订单详情中的日志，了解当前正在执行的步骤。如果怀疑卡住，可以联系系统管理员检查后台任务。

-   **Q: 如何知道哪些设备被自动退池了？**
    A: 查看对应的退池订单详情，其中会列出被退回的设备信息。

(更多FAQ可根据实际情况补充)