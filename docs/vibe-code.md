# Vibe Code

Welcome to Vibe coding...

学习：

deepwiki opendeepwiki (gpt & gemini)
deepwiki页面下RAG
示例 学习查看kubelet源码

Code:

AI Code发展过程 
0 - ide补全
1 - ai问答
2 - 人机结对
3 - AI Agent

工具介绍：
工具 <img src="https://www.cursor.com/favicon.ico" alt="Cursor" style="height:1em;vertical-align:-0.15em;"/> Cursor, <img src="https://windsurf.com/favicon.ico" alt="Windsurf" style="height:1em;vertical-align:-0.15em;"/> Windsurf, <img src="https://www.trae.ai/favicon.ico" alt="Trae" style="height:1em;vertical-align:-0.15em;"/> Trae
插件 <img src="https://www.augmentcode.com/favicon.ico" alt="Augment" style="height:1em;vertical-align:-0.15em;"/> Augment, <img src="https://cdn.jsdelivr.net/gh/lobehub/lobe-icons/icons/githubcopilot.svg" alt="Copilot" style="height:1em;vertical-align:-0.15em;"/> Copilot, <img src="https://roocode.com/favicon.ico" alt="Roo" style="height:1em;vertical-align:-0.15em;"/> Roo, <img src="https://cdn.jsdelivr.net/gh/lobehub/lobe-icons/icons/cline.svg" alt="Cline" style="height:1em;vertical-align:-0.15em;"/> Cline(可对接本地模型)

编程模型推荐：
最优编码模型(claude3.7 gemini2.5 pro)

AI Agent原理演示：
repo prompt （agent编码原理, 演示提示词）

VibeCode调优：

MCP Conetxt7/code rules/mem0

行内落地例子：
navy-设备管理&资源管理原型(行内commit技巧)

VibeCode实践：

Idea -> 市场与竞品分析（chatgpt deepresearch）-> 形成设计文档和需求文档(chatgpt) -> 代码实现(gemini2.5 pro) -> 代码审查(claude3.7 sonnet) -> 代码优化(gemini2.5 pro) -> 代码测试(claude3.7 sonnet) -> 使用文档(gemini2.5 pro)


## 引言

人工智能（AI），特别是大型语言模型（LLM），正在彻底改变软件开发。本次演示将探讨 AI 在理解代码库、高级编程辅助以及 AI 智能体兴起方面的作用。我们将介绍塑造编码未来的关键工具、模型和概念。

## 第一部分：AI 驱动的代码库理解

理解大型代码库是一项重大挑战。AI 工具正在涌现，以自动化文档生成、可视化架构并实现深度查询。

### 1.1 DeepWiki：AI 驱动的 GitHub 导航

*   **简介：** 由 Cognition AI（Devin 的创建者）推出的 AI 平台，可将公开的 GitHub 仓库转变为交互式维基。
*   **目标：** 帮助开发者快速理解陌生的代码库。
*   **核心功能：**
    *   从代码、README、配置文件生成丰富的文档。
    *   创建交互式图表（类层次结构、依赖关系）。
    *   提供 AI 聊天助手（由 Devin 驱动）回答代码问题。
    *   支持深度研究查询（发现错误、优化建议）。
*   **如何使用：** 只需将 URL 中的 "github" 替换为 "deepwiki"。已索引超过 3 万个仓库。
*   **技术：** 使用 Devin AI 进行代码理解，LLM 生成文档，RAG 用于问答。
*   **价值：** 加速学习、上手和开源贡献。*注意：效果与 Devin 的能力相关，Devin 的能力受到了一些审视。*

### 1.2 OpenDeepWiki：开源知识平台

*   **简介：** 受 DeepWiki 启发的开源替代方案。
*   **目标：** 为代码库提供强大的知识管理和协作平台。
*   **核心功能：**
    *   代码分析和概念理解。
    *   自动生成文档和 README。
    *   支持 GitHub、GitLab、Gitee、Gitea。
    *   使用 Mermaid 图表可视化代码结构。
    *   支持自定义 AI 模型（OpenAI、Azure、Anthropic 等）。
    *   AI 驱动的分析和对话式交互。
*   **MCP 支持：** 可作为模型上下文协议（MCP）服务器，允许其他 AI 工具（如 Cursor）利用其分析能力。

### 1.3 LLM（GPT、Grok）在代码理解中的作用

*   **普遍作用：** LLM 为 DeepWiki/OpenDeepWiki 等工具提供了自然语言和代码理解能力。
*   **Grok DeepSearch：** xAI 的模型，优化用于利用实时网络/X 数据进行深度研究。速度快但有时肤浅；信源质量至关重要。
*   **Devin AI（DeepWiki 引擎）：** 为软件工程任务（构建应用、修复错误）设计的复杂 AI 系统。展现了潜力，但在实际测试中也存在局限性（速度、准确性）。
*   **核心要点：** AI 工具加速理解，但批判性地评估 AI 生成的信息仍然至关重要。

---

## 第二部分：前沿阵地：LLM 编程能力的飞跃

领先的 LLM 在代码生成、调试和智能体能力方面正迅速发展。

### 2.1 Anthropic Claude 3.7：追求精准与智能体编码

*   **Claude 3.7 Sonnet：** Anthropic 的高智能模型，具有**混合推理**能力。
    *   提供即时响应和可见的、分步的“扩展思考”。
    *   在编码和 Web 开发方面有显著提升。
    *   在 SWE-bench Verified（真实世界编码任务）上表现顶尖。
    *   200K tokens 上下文窗口。
*   **Claude Code：** 命令行**智能体编码工具**（研究预览版）。
    *   **能力：** 编辑文件、修复错误、回答代码问题、运行测试、深度集成 Git（提交、PR）、理解项目结构。
    *   **工作流程：** 接收自然语言命令 -> 展示计划 -> 用户批准 -> 执行 -> 测试 -> 迭代 -> 提交。
    *   代表了 AI 向主动开发参与者的转变。

### 2.2 Google Gemini 2.5 Pro (& Flash)：海量上下文与“思考型模型”

*   **Gemini 2.5 Pro：** Google 最智能的模型，被设计为在响应前进行内部推理的**“思考型模型”**。
*   **核心特性：**
    *   编码能力较 Gemini 2.0 有巨大飞跃。
    *   巨大的 **1M-2M tokens 上下文窗口**（可分析约 3 万行代码）。
    *   原生**多模态**能力（文本、音频、图像、视频、代码库）。
    *   在 LiveCodeBench、Aider Polyglot、MRCR（长文档理解）等基准测试中表现强劲。
*   **Gemini Code Assist：** IDE 插件，提供代码解释、生成、补全、智能操作和本地代码库感知。
*   **Gemini 2.5 Flash：** 更快、成本更低的版本，优化用于高并发任务，具有可控的“思考预算”。

### 2.3 Claude 3.7 Sonnet vs. Gemini 2.5 Pro：编码对决

| 特性 | Claude 3.7 Sonnet (+ Code) | Gemini 2.5 Pro (+ Code Assist) | 主要差异 |
| :------------------- | :------------------------- | :----------------------------- | :------------------------- |
| **推理** | 混合（可见扩展思考） | 内部“思考型模型” | Claude 更透明 |
| **智能体工具** | Claude Code (CLI) | Code Assist (IDE 插件) | 集成方式不同 |
| **上下文窗口** | 200K tokens | 1M-2M tokens | Gemini 处理上下文范围大得多 |
| **SWE-bench Verified** | **70.3%** | 63.8% | Claude 在真实世界任务上领先 |
| **Aider Polyglot** | 64.9% | **74.0%** | Gemini 跨语言编辑更强 |
| **AIME (数学)** | 80.0% | **92.0%** | Gemini 数学编码更强 |
| **速度** | 优先考虑精度 | 通常更快 | Gemini 通常更快 |
| **成本** | 更高 | 更低（据称） | Gemini 更易获取 |
| **最佳适用场景** | 精度、复杂问题解决、教学 | 速度、大型项目、数学/创意编码、多模态 | 根据优先级选择 |

*注意：巨大的上下文窗口很有用，但有效的利用（“大海捞针”问题）是关键。Gemini 强大的 MRCR 得分表明其长上下文性能良好。*

---

## 第三部分：开发者超级工具：现代 AI IDE 与插件

AI 正被深度集成到开发环境中，超越了简单的自动补全。

### 3.1 AI 原生 IDE（通常是 VS Code 分支）

*   **Cursor：** 专注于生产力。
    *   特性：Tab 补全、AI 聊天结对程序员、Cmd-K 行内编辑。通过 `@Docs` 获取上下文。
*   **Windsurf：** 旨在实现“心流状态”。
    *   特性：Cascade（智能体聊天机器人）、Flows（智能体协作）、Supercomplete、自然语言终端命令。支持 MCP。
*   **Trae：** 具有强大 AI 集成的免费 IDE。
    *   特性：Builder 模式（项目生成）、AI 代码助手（聊天）、智能自动补全、多模态输入（图像）。

### 3.2 智能编辑器插件（增强现有 IDE）

*   **Roo Code (VS Code)：** 自主编码智能体。
    *   特性：自然语言控制、文件/终端访问、浏览器自动化、自定义模式（适应性角色）、MCP 集成。BYOK 模型（成本可变）。
*   **Cline (VS Code)：** 强调人机协作的 AI 结对程序员。
    *   特性：灵活的模型/API 支持、终端执行、文件编辑（差异视图）、浏览器使用（通过 Claude）、MCP 市场。BYOK 模型。
*   **Augment Code (VS Code, JetBrains, Vim)：** 专注于代码库理解。
    *   特性：上下文感知辅助、“Next Edit”引导式编辑、实验性智能体功能（JetBrains）。使用自有模型（可能降低其他平台成本）。

### 3.3 趋势与比较

*   **AI 助手级别：** 0（自动补全）-> 1（AI 自动补全）-> 2（AI 结对程序员）-> 3（AI 智能体）。
*   **AI 在代码生成中的应用：** 预计到 2025 年底将生成 20% 的代码。存在风险（可见性差距、低信誉 LLM）。
*   **IDE vs. 插件：** 集成套件与最佳组件的选择。
*   **关键主题：** 多模态输入、自然语言命令、BYOK vs. 订阅模式。

**(如果演示需要，可以在此处插入原始报告中 IDE 和插件的对比表格，可进行简化。)**

---

## 第四部分：AI 智能体与上下文窗口：记忆的边界

AI 智能体需要“记忆”来有效执行复杂任务。上下文窗口是其中的核心部分，但存在局限性。

### 4.1 上下文窗口解释

*   **定义：** LLM 一次可以处理的信息量（tokens）。如同短期/工作记忆。
*   **大小：** 以 tokens 为单位（英语中约 0.75 词/token）。已显著增长（例如，GPT-2: 2K -> Gemini 2.5 Pro: 2M）。
*   **原理：** 注意力机制权衡窗口内 token 的重要性。

### 4.2 上下文窗口对智能体的重要性

*   **连贯性：** 维持对话流程。
*   **复杂任务：** 支持处理大型输入（文档、代码库）。
*   **上下文学习：** 支持从提示中的大量示例中学习。
*   **智能体工作流：** 是规划和执行多步骤任务的基础。

### 4.3 上下文窗口的挑战

*   **固定大小/遗忘：** 窗口填满时，旧信息会丢失。限制长期记忆。
*   **成本与延迟：** 处理大上下文计算成本高。
*   **“大海捞针”：** 模型可能难以在庞大上下文中找到关键信息，或只关注开头/结尾。准确性可能下降。
*   **可扩展性：** 即便是 2M tokens 对于极长交互也可能不够。
*   **非真正记忆：** 缺乏人类记忆的持久性、结构化和优先级。

### 4.4 管理上下文：超越大小的策略

*   **基础：** 提示工程、分块/切分文本。
*   **总结/压缩：** 压缩旧信息以节省空间/成本（例如 OpenHands 方法）。
*   **检索增强生成 (RAG)：** 将信息存储在外部（如向量数据库），按需检索相关片段。适用于事实性信息，减少幻觉，但检索可能不精确。
    *   ``mermaid
        graph TD
            A[用户查询] --> B{LLM + RAG模块};
            B --> C;
            C -- 相关文档块 --> B;
            C --> D[外部知识库 (向量数据库)];
            D -- 文档块 --> C;
            B --> E[生成响应];
*   **高级记忆架构：**
    *   外部记忆系统/知识图谱。
    *   Mem0：持久化、结构化的记忆系统。
    *   智能体链 (CoA)：多智能体协作处理长上下文。
*   **缓存：** 存储中间计算结果以减少冗余。

**核心要点：** 仅仅增大窗口大小是不够的。高效的策略（RAG、压缩、高级记忆）对于平衡性能、成本和真正的长期回忆至关重要。

**(如果演示需要，可以在此处插入原始报告中上下文管理技术的对比表格，可进行简化。)**

---

## 第五部分：AI 智能体实践：代码库提示与核心原理

AI 智能体需要上下文和结构才能有效运作，尤其是在处理代码时。

### 5.1 “代码库提示”：赋予 AI 代码库上下文

*   **定义：** 向 AI 智能体提供整个代码库（或其特定部分）的上下文，以提高任务性能。
*   **目的：** 通过理解项目生态系统，实现更准确的代码生成、分析和问答。
*   **示例：**
    *   **Repo Prompt (macOS 应用)：** 选择本地文件/文件夹作为 AI 提示的上下文，审查 AI 更改。
    *   **DocsBot GitHub 智能体：** 根据提示检索仓库信息（结构、文件、提交）。
    *   **CodeGPT 智能体：** 将 GitHub/GitLab 仓库链接为智能体的“知识”。
    *   **NVIDIA 智能体工具包：** 测试驱动编码智能体使用仓库上下文的示例。
*   **重要性：** 标志着从处理孤立代码片段到理解和操作整个项目生态系统的转变。

### 5.2 AI 智能体原理：“大脑”及其组件

*   **定义：** 一个能够感知、决策并自主行动以实现目标的 AI 系统。
*   **核心架构：**
    *   **感知：** 从环境收集数据（API、文件、传感器）。
    *   **推理/规划（LLM 核心）：** 处理信息，分解任务，使用 LLM 作为“大脑”规划行动。
    *   **行动：** 执行计划（调用工具、写入文件、运行命令）。
    *   **记忆：** 存储/检索信息（短期上下文窗口，长期外部存储）。
    *   **（学习）：** 根据反馈进行调整。
*   **认知架构：** 对这些组件进行更高级别的编排（例如，线性链、图）以处理复杂任务。


### 5.3 控制循环：智能体如何协调

*   **智能体循环/认知周期：** 基本的观察 -> 定位 -> 决策 -> 行动 -> 学习循环。
*   **OODA 循环（观察、定位、决策、行动）：** 源自军事战略，强调在动态环境中的快速决策。
*   **ReAct 框架（推理与行动）：** 结合了思维链（CoT）推理和外部工具的使用。
    *   迭代循环：思考 -> 行动（工具使用）-> 观察（工具结果）-> 思考...
    *   优点：适应性强、可解释（显示推理过程）、准确（使用外部信息）。


**核心要点：** LLM 作为推理核心，被集成到结构化框架（如 OODA、ReAct）中，使智能体能够有目的地与环境和工具交互。

## 结论与展望

AI 正通过增强代码理解、生成能力和复杂的 AI 智能体，从根本上重塑软件开发。

*   **关键进展：** AI 驱动的文档（DeepWiki）、强大的编码 LLM（Claude 3.7、Gemini 2.5 Pro）、集成的 AI IDE/插件（Cursor、Windsurf、Roo Code、Cline）以及不断发展的智能体能力。
*   **核心挑战：** 有效管理上下文（超越更大的窗口）并确保 AI 的准确性/可靠性至关重要。
*   **未来趋势：**
    *   更深度的 AI-IDE 集成。
    *   更自主、能力更强的 AI 智能体。
    *   成熟的上下文/记忆管理技术。
    *   多模态交互的增加。
    *   开发者技能向 AI 协作和监督转变。
*   **注意事项：** 对 AI 错误、偏见和安全风险保持警惕是必要的。

编码的未来是协作性的，AI 将扮演强大的伙伴角色，这要求开发者具备新技能并进行谨慎的集成。


## 学习资料

* MCP（https://huggingface.co/learn/mcp-course）
* mem0 (https://mem0.ai/)
* context7 ((https://context7.com/)

