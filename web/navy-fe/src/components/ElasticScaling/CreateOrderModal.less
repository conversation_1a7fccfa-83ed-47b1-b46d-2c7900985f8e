.create-order-modal {
  /* 模态框样式已统一到 Dashboard.css 中 */

  .ant-form-item-label > label {
    font-weight: 500;
    font-size: 14px;
  }

  .ant-card {
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);

    .ant-card-head {
      background-color: #f5f7fa;
      min-height: 40px;
      padding: 0 16px;

      .ant-card-head-title {
        padding: 10px 0;
      }
    }

    .ant-card-body {
      padding: 16px 24px;
    }
  }

  .ant-input, .ant-select-selector, .ant-input-number {
    border-radius: 4px;
  }

  .ant-divider {
    margin: 16px 0;

    .ant-divider-inner-text {
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }
  }

  .ant-table-thead > tr > th {
    background-color: #f5f7fa;
    font-weight: 500;
    padding: 12px 16px;
  }

  .ant-table-tbody > tr > td {
    padding: 12px 16px;
  }

  .ant-table-row:hover {
    background-color: #f0f7ff;
  }

  // 筛选组样式
  .filter-group {
    margin-bottom: 16px;
    padding: 12px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    background-color: #fafafa;

    &:last-child {
      margin-bottom: 0;
    }

    .filter-group-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      padding-bottom: 8px;
      border-bottom: 1px dashed #f0f0f0;

      span {
        font-weight: 500;
        color: rgba(0, 0, 0, 0.65);
      }
    }

    .ant-tag {
      margin: 4px;
      padding: 4px 8px;
      border-radius: 4px;
      background-color: #f5f7fa;
      border-color: #e8e8e8;
      color: rgba(0, 0, 0, 0.65);

      &:hover {
        border-color: #1890ff;
      }

      .anticon-close {
        color: rgba(0, 0, 0, 0.45);

        &:hover {
          color: rgba(0, 0, 0, 0.85);
        }
      }
    }
  }

  // 设备列表摘要行
  .ant-table-summary {
    .ant-table-cell {
      background-color: #f5f7fa;
      font-weight: normal;

      strong {
        color: #1890ff;
        font-weight: 600;
      }
    }
  }
  
  // 设备标签样式
  .device-tags-container {
    padding: 30px;
    background: linear-gradient(to bottom, #ffffff, #f9fbfd);
    border-radius: 12px;
    min-height: 220px;
    border: 1px solid #e6e6e6;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    

    
    &:hover {
      box-shadow: 0 15px 35px rgba(24, 144, 255, 0.15);
      transform: translateY(-2px);
    }
    
    .device-tag {
      padding: 10px 16px;
      margin-bottom: 16px;
      margin-right: 16px;
      font-size: 14px;
      display: inline-flex;
      align-items: center;
      background: #fff;
      border: 1px solid rgba(24, 144, 255, 0.3);
      color: #1890ff;
      border-radius: 10px;
      transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);
      position: relative;
      overflow: hidden;
      

      
      &:hover {
        background-color: #f0f9ff;
        border-color: #1890ff;
        transform: translateY(-3px);
        box-shadow: 0 8px 16px rgba(24, 144, 255, 0.15);
        

      }
      
      .ant-tag-close-icon {
        margin-left: 10px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 12px;
        background-color: rgba(255, 255, 255, 0.8);
        border-radius: 50%;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        border: 1px solid transparent;
        
        &:hover {
          color: #ff4d4f;
          background-color: #fff;
          border-color: #ffccc7;
          box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
        }
      }
    }
    
    .device-count {
      margin-bottom: 24px;
      font-size: 16px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);
      padding-bottom: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      

      
      strong {
        color: #1890ff;
        font-weight: 600;
        font-size: 20px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        color: #1890ff;
      }
    }
  }
}

// 设备标签动画
@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.85) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
