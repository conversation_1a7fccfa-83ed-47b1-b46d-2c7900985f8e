/* 设备管理页面样式 */

/* 卡片样式 */
.device-management-card {
  box-shadow: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);
  border-radius: 8px;
  border: none;
  overflow: hidden;
  transition: all 0.3s;
}

.device-management-card .ant-card-head {
  background: linear-gradient(to right, rgba(22, 119, 255, 0.05), rgba(22, 119, 255, 0.01));
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.device-management-card .ant-card-head-title {
  font-size: 18px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
}

/* 搜索区域样式 */
.search-container {
  display: flex;
  align-items: center;
}

/* === 表格核心样式 === */
.device-management-card .ant-table-wrapper {
  background: white;
  border-radius: 8px; /* Apply border radius to wrapper */
  overflow: hidden; /* Ensures shadow is clipped */
}

/* 表头和单元格通用样式 */
.device-management-card .ant-table-thead > tr > th,
.device-management-card .ant-table-tbody > tr > td {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 10px 8px; /* Consistent padding */
  height: 46px;
  line-height: 26px;
  vertical-align: middle; /* Ensure vertical alignment */
}

.device-management-card .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
}

/* 固定列样式 */
.device-management-card .ant-table-cell-fix-right {
  position: sticky !important; /* Ensure sticky positioning */
  right: 0;
  background: #ffffff !important; /* Force solid white background */
  z-index: 1 !important; /* Ensure it's above other cells */
}

.device-management-card .ant-table-tbody > tr > .ant-table-cell-fix-right {
   background: #ffffff !important; /* Apply to body cells as well */
}

/* 固定列阴影 (只在第一个固定列上) */
.device-management-card .ant-table-cell-fix-right.ant-table-cell-fix-right-first {
  box-shadow: -6px 0 6px -6px rgba(0, 0, 0, 0.1) !important; /* Keep the shadow */
}

/* 操作列 Space 容器 */
.device-management-card .ant-table-cell-fix-right .action-space {
  display: flex !important;
  gap: 4px !important;
  flex-wrap: nowrap !important;
  align-items: center;
  justify-content: center; /* Center buttons within the cell */
  height: 100%; /* Ensure space takes full height */
}

/* 操作按钮具体样式 */
.device-management-card .action-button.ant-btn-link {
  min-width: 40px;
  height: 24px;
  padding: 0 4px;
  margin: 0;
  font-size: 13px;
  line-height: 24px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #1677ff;
  background: transparent;
  border: none;
}

.device-management-card .action-button.ant-btn-link:hover {
  color: #4096ff;
  background-color: rgba(22, 119, 255, 0.05);
  text-decoration: none;
}

/* 分页器样式 */
.device-management-card .ant-pagination {
  margin-top: 16px;
  padding: 0 8px;
}

.device-management-card .ant-pagination-item-active {
  border-color: #1677ff;
}

/* === 响应式调整 === */
@media (max-width: 1400px) {
  .device-management-card .ant-table-thead > tr > th,
  .device-management-card .ant-table-tbody > tr > td {
    padding: 8px 6px;
    height: 42px;
    line-height: 26px;
  }

  .device-management-card .action-button.ant-btn-link {
    font-size: 12px;
    min-width: 36px;
    padding: 0 2px;
  }
}

/* === 移除旧/冲突样式 === */
/* All previous conflicting attempts should be removed by replacing the file content */

/* 设备特性提示框样式 */
.device-feature-tooltip {
  animation: fadeIn 0.2s ease-in-out;
  transform-origin: top left;
  pointer-events: none; /* 确保提示框不会阻止鼠标事件 */
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 设备详情页样式 */
.device-detail-card {
  box-shadow: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);
  border-radius: 8px;
  border: none;
  overflow: hidden;
  transition: all 0.3s;
  margin-bottom: 24px;
}

.device-detail-card .ant-card-head {
  background: linear-gradient(to right, rgba(22, 119, 255, 0.05), rgba(22, 119, 255, 0.01));
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.device-detail-card .ant-card-body {
  padding: 24px;
}

/* 详情区域样式 */
.detail-section {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px rgba(0, 0, 0, 0.02);
  margin-bottom: 24px;
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(to right, rgba(22, 119, 255, 0.05), rgba(22, 119, 255, 0.01));
  border-bottom: 1px solid #f0f0f0;
}

.section-icon {
  font-size: 18px;
  color: #1890ff;
  margin-right: 10px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.section-content {
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

/* 字段项样式 */
.field-item {
  display: flex;
  align-items: flex-start;
}

.field-label {
  min-width: 120px;
  color: rgba(0, 0, 0, 0.65);
  margin-right: 8px;
  text-align: right;
  flex-shrink: 0;
}

.field-value {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  word-break: break-word;
  flex: 1;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .section-content {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .section-content {
    grid-template-columns: 1fr;
  }
}
