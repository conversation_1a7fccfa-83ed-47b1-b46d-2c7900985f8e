/* 设备中心样式 */

.device-center-container {
  width: 100%;
}

.device-center-card {
  margin-bottom: 24px;
}

/* 查询面板样式 */
.simple-query-panel {
  padding: 16px 0;
}

.query-tips {
  color: #888;
  font-size: 12px;
  margin-top: 8px;
}

/* 高级查询面板样式 */
.advanced-query-panel {
  padding: 16px 0;
}

/* 空状态提示 */
.empty-groups, .empty-blocks {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32px 0;
  background: #f9f9f9;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
  margin-bottom: 16px;
  transition: all 0.3s;
}

.empty-groups:hover, .empty-blocks:hover {
  border-color: #1890ff;
  background: #f0f7ff;
}

/* 条件组样式 */
.filter-group {
  background: #ffffff;
  border-radius: 8px;
  padding: 0;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.filter-group:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.filter-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(to right, rgba(24, 144, 255, 0.05), rgba(24, 144, 255, 0.01));
  border-bottom: 1px solid #f0f0f0;
}

.filter-blocks {
  padding: 16px;
}

/* 条件块样式 */
.filter-block {
  background: #fafafa;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  position: relative;
}

.filter-block:hover {
  border-color: #1890ff;
  background: #f0f7ff;
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.1);
}

.filter-block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px dashed #f0f0f0;
}

.filter-block-content {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.filter-block-item {
  flex: 1;
  min-width: 200px;
}

.filter-block-item .ant-typography {
  display: block;
  margin-bottom: 4px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 13px;
}

/* 条件选择样式 */
.filter-block-item .ant-select-item-option-content .ant-tag {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  font-weight: bold;
}

.filter-block-item .ant-select-selection-item {
  font-weight: 500;
}

/* 按钮区域样式 */
.filter-block-actions {
  display: flex;
  justify-content: flex-start;
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px dashed #f0f0f0;
}

.filter-block-actions .ant-btn {
  border-radius: 4px;
  transition: all 0.3s;
}

.filter-group-actions {
  display: flex;
  justify-content: center;
  margin: 16px 0;
}

.filter-group-actions .ant-btn {
  border-radius: 4px;
  padding: 0 24px;
  height: 40px;
  transition: all 0.3s;
  border-style: dashed;
}

.filter-group-actions .ant-btn:hover {
  color: #1890ff;
  border-color: #1890ff;
  background: #f0f7ff;
  transform: translateY(-2px);
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.1);
}

.query-actions {
  display: flex;
  justify-content: flex-start;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.query-actions .ant-btn {
  min-width: 100px;
  height: 40px;
  border-radius: 4px;
  transition: all 0.3s;
}

.query-actions .ant-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
}

/* 模板面板样式 */
.templates-panel {
  padding: 16px 0;
}

.template-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 16px;
  min-height: 200px; /* 确保即使模板很少也有一定的高度 */
}

.template-card {
  margin-bottom: 0;
  height: 100%;
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.template-card:hover {
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.15);
  transform: translateY(-3px);
  border-color: #1890ff;
}

.template-card .ant-card-head {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.08), rgba(24, 144, 255, 0.02));
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 16px;
}

.template-card .ant-card-extra .ant-btn {
  transition: all 0.3s;
  margin-left: 6px;
}

.template-card .ant-card-extra .ant-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.template-card .ant-card-extra .ant-btn-primary {
  background: linear-gradient(135deg, #1890ff, #096dd9);
}

.template-card .ant-card-extra .ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff, #1890ff);
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);
}

.template-card .ant-card-extra .ant-btn-dangerous:hover {
  box-shadow: 0 2px 6px rgba(255, 77, 79, 0.2);
}

.template-card .ant-card-body {
  padding: 16px 20px 20px;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0), rgba(24, 144, 255, 0.02));
}

.template-card .ant-typography {
  margin-bottom: 12px;
  line-height: 1.6;
}

.template-card-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
}

/* 模板分页器样式 */
.template-pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 查询摘要样式 */
.query-summary {
  background: #f5f5f5;
  border-radius: 4px;
  padding: 8px 12px;
  margin-bottom: 16px;
}

/* 表格操作区域 */
.table-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 动画效果 */
.filter-group-wrapper {
  animation: fadeInDown 0.3s ease-out;
}

.filter-block-wrapper {
  animation: fadeInScale 0.3s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 用途编辑对话框样式 */
.group-edit-modal .ant-modal-content {
  border-radius: 8px;
  overflow: hidden;
}

.group-edit-modal .ant-modal-header {
  background: linear-gradient(to right, rgba(24, 144, 255, 0.05), rgba(24, 144, 255, 0.01));
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.group-edit-modal .ant-modal-body {
  padding: 24px;
}

/* 用途下拉框样式 */
.ant-select-dropdown .ant-select-item-option {
  padding: 8px 12px;
  transition: all 0.3s;
}

.ant-select-dropdown .ant-select-item-option:hover {
  background-color: #f5f5f5;
}

.ant-select-dropdown .ant-select-item-option-selected {
  font-weight: 500;
  background-color: #e6f7ff;
}

/* 标签样式 */
.ant-select-selection-item-content {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .filter-block-content {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-block-item {
    width: 100%;
  }

  .filter-block-actions,
  .filter-group-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-block-actions .ant-btn,
  .filter-group-actions .ant-btn {
    margin-bottom: 8px;
    width: 100%;
  }

  .query-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .query-actions .ant-btn {
    margin-bottom: 8px;
    width: 100%;
  }

  .filter-group-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .filter-group-header .ant-select {
    width: 100%;
  }
}

/* 设备特性提示框样式 */
.device-feature-tooltip {
  min-width: 200px;
  max-width: 400px;
}

.device-feature-tooltip .tooltip-title {
  font-weight: bold;
  margin-bottom: 8px;
  padding-bottom: 4px;
  border-bottom: 1px solid #f0f0f0;
}

.device-feature-tooltip .tooltip-content {
  font-size: 12px;
  line-height: 1.5;
}

.device-feature-tooltip .tooltip-content > div {
  margin-bottom: 4px;
  padding: 2px 4px;
  background-color: #f9f9f9;
  border-radius: 2px;
}

/* 机器用途文本样式 */
.group-text {
  position: relative;
  display: inline-block;
  max-width: 140px; /* 宽度设置为正好显示10个中文字符 */
  overflow: hidden;
  white-space: nowrap;
  vertical-align: middle;
  padding: 2px 0;
  color: #333;
  font-size: 14px;
}

/* 优雅的截断样式 */
.group-text.has-more {
  position: relative;
  padding-right: 4px;
  text-overflow: ellipsis;
}

/* 默认渐变效果（白色背景） */
.group-text.has-more::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 80px; /* 增加宽度，从第6个字符开始渐变 */
  background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.7) 50%, rgba(255, 255, 255, 1) 90%);
  pointer-events: none;
}

/* 特殊设备渐变效果（浅黄色背景 #fffbe6） */
.group-text.has-more.special-bg::after {
  background: linear-gradient(to right, rgba(255, 251, 230, 0), rgba(255, 251, 230, 0.7) 50%, rgba(255, 251, 230, 1) 90%);
}

/* 集群设备渐变效果（浅绿色背景 #f6ffed） */
.group-text.has-more.cluster-bg::after {
  background: linear-gradient(to right, rgba(246, 255, 237, 0), rgba(246, 255, 237, 0.7) 50%, rgba(246, 255, 237, 1) 90%);
}

/* 强制下拉框朝下显示 */
.group-edit-modal .ant-select-dropdown {
  top: 100% !important;
  bottom: auto !important;
  margin-top: 4px !important;
}

/* 空状态模板页面样式 */
.empty-template-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #f7f9fc 100%);
  border-radius: 16px;
  min-height: 400px;
  text-align: center;
  border: 1px dashed rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
  box-shadow: inset 0 0 20px rgba(24, 144, 255, 0.05);
}

.empty-template-container:hover {
  border-color: #1890ff;
  box-shadow: 0 8px 20px rgba(24, 144, 255, 0.15), inset 0 0 30px rgba(24, 144, 255, 0.08);
  transform: translateY(-5px);
}

.empty-template-icon {
  font-size: 72px;
  color: #1890ff;
  margin-bottom: 28px;
  animation: float 3s ease-in-out infinite;
  text-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
  background: linear-gradient(135deg, #40a9ff, #1890ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.empty-template-title {
  font-size: 28px;
  color: #262626;
  margin-bottom: 20px;
  font-weight: 600;
  letter-spacing: 0.5px;
  background: linear-gradient(135deg, #333, #555);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.empty-template-description {
  font-size: 16px;
  color: #8c8c8c;
  max-width: 550px;
  margin-bottom: 36px;
  line-height: 1.8;
  background: rgba(255, 255, 255, 0.8);
  padding: 16px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.empty-template-actions {
  display: flex;
  gap: 16px;
}

.empty-template-actions .ant-btn-primary {
  background: linear-gradient(135deg, #1890ff, #096dd9);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  padding: 0 24px;
  height: 40px;
  transition: all 0.3s;
}

.empty-template-actions .ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff, #1890ff);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}
