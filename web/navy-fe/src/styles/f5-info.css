/* F5信息列表和详情样式 */

/* 现代化F5信息管理系统样式 */

/* ===== 全局变量 ===== */
:root {
  --primary-color: #1677ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --border-radius: 8px;
  --card-shadow: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);
  --transition-duration: 0.3s;
}

/* ===== 整体布局优化 ===== */
body {
  background-color: #f0f2f5;
}

/* ===== 卡片样式优化 ===== */
.f5-info-list-card,
.ant-card {
  box-shadow: var(--card-shadow);
  border-radius: var(--border-radius);
  border: none;
  overflow: hidden;
  transition: all var(--transition-duration);
}

.f5-info-list-card .ant-card-head {
  background: linear-gradient(to right, rgba(22, 119, 255, 0.05), rgba(22, 119, 255, 0.01));
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.f5-info-list-card .ant-card-head-title {
  font-size: 18px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
}

/* ===== 表格样式优化 ===== */
.ant-table {
  border-radius: var(--border-radius);
}

.ant-table-wrapper {
  background: white;
  border-radius: var(--border-radius);
  overflow: hidden;
}

.ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  padding: 16px;
  transition: background var(--transition-duration);
}

.ant-table-tbody > tr > td {
  padding: 16px;
  transition: all var(--transition-duration);
}

.ant-table-tbody > tr:hover > td {
  background-color: rgba(22, 119, 255, 0.05) !important;
}

/* 行样式优化 */
.status-active-row,
.status-online-row,
.status-running-row,
.status-healthy-row {
  background-color: rgba(82, 196, 26, 0.05) !important;
}

.status-inactive-row,
.status-offline-row,
.status-stopped-row {
  background-color: rgba(255, 77, 79, 0.05) !important;
}

.status-degraded-row,
.status-warning-row {
  background-color: rgba(250, 173, 20, 0.05) !important;
}

.ignored-row {
  background-color: rgba(250, 173, 20, 0.08) !important;
  border-left: 2px solid #faad14;
}

/* 行悬停效果 */
.status-active-row:hover td,
.status-online-row:hover td,
.status-running-row:hover td,
.status-healthy-row:hover td {
  background-color: rgba(82, 196, 26, 0.1) !important;
}

.status-inactive-row:hover td,
.status-offline-row:hover td,
.status-stopped-row:hover td {
  background-color: rgba(255, 77, 79, 0.1) !important;
}

.status-degraded-row:hover td,
.status-warning-row:hover td {
  background-color: rgba(250, 173, 20, 0.1) !important;
}

.ignored-row:hover td {
  background-color: rgba(250, 173, 20, 0.15) !important;
}

/* ===== 按钮样式优化 ===== */
.ant-btn {
  border-radius: 4px;
  transition: all 0.2s;
}

.ant-btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

.ant-btn-link {
  color: var(--primary-color);
}

.ant-btn-link:hover {
  color: #4096ff;
  text-decoration: underline;
}

/* ===== 标签样式优化 ===== */
.ant-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  line-height: 1;
  margin-right: 8px;
  border: none;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.06);
}

/* ===== 详情页样式优化 ===== */
.ant-descriptions {
  background: white;
  border-radius: var(--border-radius);
}

.ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: #fafafa;
  font-weight: 500;
}

.ant-descriptions-bordered .ant-descriptions-view {
  border: 1px solid #f0f0f0;
  border-radius: var(--border-radius);
  overflow: hidden;
}

/* 忽略状态的详情卡片 */
.ignored-detail-card {
  opacity: 0.95;
  border-left: 2px solid #faad14;
}

.ignored-detail-card .ant-descriptions-bordered .ant-descriptions-view {
  border: 1px solid #ffe58f;
}

/* ===== 池成员列表样式优化 ===== */
.ant-list-bordered {
  border-radius: var(--border-radius);
  overflow: hidden;
  border: 1px solid #f0f0f0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.ant-list-item {
  padding: 12px 16px;
  transition: background-color var(--transition-duration);
}

.ant-list-item:not(:last-child) {
  border-bottom: 1px solid #f0f0f0;
}

.ant-list-item:hover {
  background-color: rgba(22, 119, 255, 0.05);
}

/* ===== 模态框样式优化 ===== */
.ant-modal-content {
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--card-shadow);
}

.ant-modal-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  background: linear-gradient(to right, rgba(22, 119, 255, 0.05), rgba(22, 119, 255, 0.01));
}

.ant-modal-title {
  font-weight: 600;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
}

.ant-modal-close {
  color: rgba(0, 0, 0, 0.45);
  transition: all 0.3s;
}

.ant-modal-close:hover {
  color: var(--primary-color);
  background-color: rgba(0, 0, 0, 0.04);
}

.ant-modal-body {
  padding: 24px;
}

.ant-modal-footer {
  border-top: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.ant-modal-mask {
  backdrop-filter: blur(2px);
  background-color: rgba(0, 0, 0, 0.45);
}

.ant-modal-footer .ant-btn + .ant-btn {
  margin-left: 12px;
}

/* 环境选择按钮动画 */
.ant-btn {
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.ant-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.ant-modal.zoom-in-enter,
.ant-modal.zoom-in-appear {
  transform: scale(0.9);
  opacity: 0;
}

.ant-modal.zoom-in-enter-active,
.ant-modal.zoom-in-appear-active {
  transform: scale(1);
  opacity: 1;
  transition: transform 0.3s cubic-bezier(0.08, 0.82, 0.17, 1), opacity 0.3s cubic-bezier(0.08, 0.82, 0.17, 1);
}

.ant-modal.zoom-in-exit {
  transform: scale(1);
}

.ant-modal.zoom-in-exit-active {
  transform: scale(0.9);
  opacity: 0;
  transition: transform 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86), opacity 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
}

/* ===== 加载状态样式 ===== */
.ant-spin-nested-loading .ant-spin-container::after {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
}

/* ===== 状态图标尺寸优化 ===== */
.anticon-check-circle,
.anticon-close-circle,
.anticon-warning {
  font-size: 14px;
}

/* ===== 自定义字段高亮 ===== */
.field-highlight {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

/* ===== 分页器样式优化 ===== */
.ant-pagination {
  margin-top: 16px;
}

.ant-pagination-item-active {
  border-color: var(--primary-color);
}

/* ===== 响应式优化 ===== */
@media (max-width: 768px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
  }
  
  .ant-card-head-title {
    font-size: 16px;
  }
  
  .ant-descriptions-item-label,
  .ant-descriptions-item-content {
    padding: 12px 8px;
  }
} 