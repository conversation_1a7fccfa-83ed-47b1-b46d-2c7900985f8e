/* Operations Management Styles */

/* Log container */
.ops-log-container {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  height: 300px;
  overflow-y: auto;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  white-space: pre-wrap;
  font-size: 12px;
  line-height: 1.5;
  color: #333;
}

/* Log line styles */
.log-line {
  margin: 0;
  padding: 2px 0;
}

.log-line-info {
  color: #1677ff;
}

.log-line-success {
  color: #52c41a;
}

.log-line-warning {
  color: #faad14;
}

.log-line-error {
  color: #ff4d4f;
}

/* Job status colors */
.job-status-pending {
  background-color: rgba(22, 119, 255, 0.1);
}

.job-status-running {
  background-color: rgba(82, 196, 26, 0.1);
}

.job-status-completed {
  background-color: rgba(82, 196, 26, 0.1);
}

.job-status-failed {
  background-color: rgba(255, 77, 79, 0.1);
}

/* Job card styles */
.ops-job-card {
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.07);
}

.ops-job-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

/* Progress bar styles */
.ant-progress-text {
  font-weight: 500;
}

/* Drawer styles */
.ops-drawer .ant-drawer-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.ops-drawer .ant-drawer-body {
  padding: 24px;
}

/* Button styles */
.start-job-button {
  background-color: #52c41a;
  border-color: #52c41a;
}

.start-job-button:hover {
  background-color: #73d13d;
  border-color: #73d13d;
}

/* WebSocket status indicator */
.ws-status {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.ws-status-connected {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.ws-status-disconnected {
  background-color: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}
