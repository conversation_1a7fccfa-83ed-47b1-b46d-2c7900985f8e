---
description: 
globs: 
alwaysApply: true
---
# Rule: navy-ng
# Path: .cursor/rules/navy-ng.mdc
# Description: Navy-NG项目规则集索引
# Tags: overview, index, structure

# Navy-NG 项目规则集

本规则集为Navy-NG项目的主规则索引，包含了项目的各个子规则集。

## 总体概述
- [项目总体概述](mdc:.cursor/rules/overview/navy-ng-overview.mdc)

## 按目录的规则集
- [数据模型层规则](mdc:.cursor/rules/models/models-structure.mdc)
- [公共库规则](mdc:.cursor/rules/pkg/pkg-structure.mdc) 
- [后端服务规则](mdc:.cursor/rules/server/server-structure.mdc)
- [任务Job规则](mdc:.cursor/rules/job/job-structure.mdc)
- [前端应用规则](mdc:.cursor/rules/web/web-structure.mdc)
- [开发运维脚本规则](mdc:.cursor/rules/scripts/scripts-structure.mdc)

## 配置与开发规范
- [项目配置文件规则](mdc:.cursor/rules/config/project-config-files.mdc)
- [代码开发约定](mdc:.cursor/rules/development/code-conventions.mdc)

## Job相关详细规则
- [Job总体概述](mdc:.cursor/rules/job-overview.mdc)
- [安全检查收集器](mdc:.cursor/rules/security-check-collector.mdc)
- [安全报告发送器](mdc:.cursor/rules/security-report-sender.mdc)
- [数据库模型Job相关](mdc:.cursor/rules/database-models-job.mdc)

