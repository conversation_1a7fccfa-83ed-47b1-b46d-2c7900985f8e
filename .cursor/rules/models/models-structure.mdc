---
description: 
globs: 
alwaysApply: false
---
# Rule: models-structure
# Path: .cursor/rules/models/models-structure.mdc
# Description: models目录结构及使用规范
# Tags: models, gorm, database

# Models目录结构

`models/`目录包含所有数据模型层定义，使用GORM进行数据库操作。

## 目录结构
```
models/
└── portal/            # 门户模块数据模型
    └── object.go      # 核心数据模型定义文件
```

## 数据模型开发规范

1. 所有数据库模型定义应放在对应的模块目录下
2. 模型应遵循GORM的命名和标签规范,模型层不要打json标签
3. 数据库字段使用下划线命名法，但结构体字段使用驼峰命名法
4. 每个模型应包含基础字段（如ID、CreatedAt、UpdatedAt等）
5. 模型间关联应通过外键明确定义
